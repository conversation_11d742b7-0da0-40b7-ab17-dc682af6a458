import request from '@/utils/request'

// 创建学生
export function createStudent(data) {
  return request({
    url: '/infra/student/create',
    method: 'post',
    data: data
  })
}

// 更新学生
export function updateStudent(data) {
  return request({
    url: '/infra/student/update',
    method: 'put',
    data: data
  })
}

// 删除学生
export function deleteStudent(id) {
  return request({
    url: '/infra/student/delete?id=' + id,
    method: 'delete'
  })
}

// 获得学生
export function getStudent(id) {
  return request({
    url: '/infra/student/get?id=' + id,
    method: 'get'
  })
}

// 获得学生分页
export function getStudentPage(params) {
  return request({
    url: '/infra/student/page',
    method: 'get',
    params
  })
}
// 导出学生 Excel
export function exportStudentExcel(params) {
  return request({
    url: '/infra/student/export-excel',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 子表（学生联系人） ====================
  
    // 获得学生联系人列表
    export function getStudentContactListByStudentId(studentId) {
      return request({
        url: `/infra/student/student-contact/list-by-student-id?studentId=` + studentId,
        method: 'get'
      })
    }
  
// ==================== 子表（学生班主任） ====================
  
    // 获得学生班主任
    export function getStudentTeacherByStudentId(studentId) {
      return request({
        url: `/infra/student/student-teacher/get-by-student-id?studentId=` + studentId,
        method: 'get'
      })
    }
  