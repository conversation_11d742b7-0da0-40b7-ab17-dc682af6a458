package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.DigitalAssetCourseDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import java.util.Collection;
import java.util.List;

/**
 * 数字资产课程 Service 接口
 * 
 * <AUTHOR>
 */
public interface DigitalAssetCourseService {

    /**
     * 查询课程列表
     *
     * @param reqVO 查询条件
     * @return 课程列表
     */
    List<DigitalAssetCourseRespVO> listDigitalAssetCourse(DigitalAssetCourseListReqVO reqVO);

    /**
     * 分页查询课程列表
     *
     * @param reqVO 分页查询条件
     * @return 课程分页列表
     */
    PageResult<DigitalAssetCourseRespVO> pageDigitalAssetCourse(DigitalAssetCoursePageReqVO reqVO);

    /**
     * 创建课程
     * 
     * @param reqVO 创建信息
     * @return 课程ID
     */
    Long createDigitalAssetCourse(DigitalAssetCourseSaveReqVO reqVO);

    /**
     * 更新课程
     * 
     * @param reqVO 更新信息
     */
    void updateDigitalAssetCourse(DigitalAssetCourseSaveReqVO reqVO);

    /**
     * 删除课程
     * 
     * @param id 课程ID
     */
    void deleteDigitalAssetCourse(Long id);

    /**
     * 获取课程详情
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    DigitalAssetCourseRespVO getDigitalAssetCourse(Long id);

    /**
     * 修改课程状态
     * 
     * @param id 课程ID
     * @param status 课程状态
     */
    void updateDigitalAssetCourseStatus(Long id, String status);

    /**
     * 获取课程统计概览
     * 
     * @return 统计数据
     */
    CourseStatisticsRespVO getCourseStatistics();

    // ========== API接口需要的方法 ==========

    /**
     * 获取课程（用于API接口）
     * 
     * @param id 课程ID
     * @return 课程DO
     */
    DigitalAssetCourseDO getCourse(Long id);

    /**
     * 获取课程列表（用于API接口）
     * 
     * @param ids 课程ID集合
     * @return 课程DO列表
     */
    List<DigitalAssetCourseDO> getCourseList(Collection<Long> ids);
}
