package cn.bztmaster.cnt.module.publicbiz.service.teacher;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportValidateRespVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import java.util.List;

public interface TeacherService {
    Long createTeacher(TeacherSaveReqVO reqVO);
    void updateTeacher(TeacherUpdateReqVO reqVO);
    void deleteTeacher(Long id);
    TeacherRespVO getTeacherDetail(Long id);
    PageResult<TeacherRespVO> getTeacherPage(TeacherPageReqVO reqVO);
    List<TeacherListRespVO> getTeacherList();
    TeacherStatRespVO getTeacherStat();
    /**
     * 批量导入讲师（只导入校验通过的数据）
     * 
     * @param teacherList 讲师数据列表
     * @return 导入结果
     */
    TeacherImportRespVO importValidTeachers(List<TeacherSaveReqVO> teacherList);
    
    /**
     * 校验Excel导入的讲师数据
     * 
     * @param excelList Excel数据列表
     * @return 校验结果
     */
    TeacherImportValidateRespVO validateImportExcelTeachers(List<TeacherImportExcelVO> excelList);
} 