package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageCarouselDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ServicePackageCarouselConvert {
    ServicePackageCarouselConvert INSTANCE = Mappers.getMapper(ServicePackageCarouselConvert.class);

    ServicePackageCarouselDO convert(ServicePackageCarouselSaveReqVO bean);

    ServicePackageCarouselRespVO convert(ServicePackageCarouselDO bean);

    List<ServicePackageCarouselRespVO> convertList(List<ServicePackageCarouselDO> list);
} 