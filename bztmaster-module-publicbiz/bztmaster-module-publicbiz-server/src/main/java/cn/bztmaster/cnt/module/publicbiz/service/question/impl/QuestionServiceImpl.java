package cn.bztmaster.cnt.module.publicbiz.service.question.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionOptionDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionCategoryMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionOptionMapper;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;
import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

/**
 * 考题服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuestionServiceImpl implements QuestionService {

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private QuestionOptionMapper questionOptionMapper;

    @Resource
    private QuestionCategoryMapper questionCategoryMapper;

    @Override
    public PageResult<QuestionRespVO> pageQuestion(QuestionPageReqVO reqVO) {
        PageResult<QuestionDO> pageResult = questionMapper.selectPage(reqVO);
        if (pageResult.getList().isEmpty()) {
            return PageResult.empty();
        }

        // 查询选项信息
        List<Long> questionIds = pageResult.getList().stream()
                .map(QuestionDO::getId)
                .collect(Collectors.toList());
        List<QuestionOptionDO> options = questionOptionMapper.selectByQuestionIds(questionIds);
        Map<Long, List<QuestionOptionDO>> optionMap = options.stream()
                .collect(Collectors.groupingBy(QuestionOptionDO::getQuestionId));

        // 转换为VO
        List<QuestionRespVO> list = pageResult.getList().stream()
                .map(question -> {
                    QuestionRespVO vo = QuestionConvert.INSTANCE.convert(question);
                    List<QuestionOptionDO> questionOptions = optionMap.get(question.getId());
                    if (questionOptions != null) {
                        vo.setOptions(QuestionConvert.INSTANCE.convertOptionDOListToVO(questionOptions));
                    }
                    return vo;
                })
                .collect(Collectors.toList());

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public QuestionRespVO getQuestion(Long id) {
        // 查询考题基本信息
        QuestionDO question = questionMapper.selectById(id);
        if (question == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }

        // 查询选项信息
        List<QuestionOptionDO> options = questionOptionMapper.selectByQuestionId(id);

        // 转换为VO
        QuestionRespVO vo = QuestionConvert.INSTANCE.convert(question);
        vo.setOptions(QuestionConvert.INSTANCE.convertOptionDOListToVO(options));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = QUESTION_TYPE, subType = QUESTION_CREATE_SUB_TYPE, bizNo = "{{#question.id}}", success = QUESTION_CREATE_SUCCESS)
    public Long createQuestion(QuestionSaveReqVO reqVO) {
        // 转换并插入考题
        QuestionDO question = QuestionConvert.INSTANCE.convert(reqVO);
        question.setStatus(1); // 默认启用
        question.setCreatorName(SecurityFrameworkUtils.getLoginUserNickname()); // 设置创建人姓名
        questionMapper.insert(question);

        // 插入选项
        if (reqVO.getOptions() != null && !reqVO.getOptions().isEmpty()) {
            List<QuestionOptionDO> options = QuestionConvert.INSTANCE.convertOptionVOListToDO(reqVO.getOptions());
            options.forEach(option -> {
                option.setQuestionId(question.getId());
                option.setTenantId(TenantContextHolder.getTenantId()); // 设置租户ID
            });
            questionOptionMapper.insertBatch(options);
        }

        // 记录操作日志上下文
        LogRecordContext.putVariable("question", question);
        return question.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = QUESTION_TYPE, subType = QUESTION_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}", success = QUESTION_UPDATE_SUCCESS)
    public void updateQuestion(QuestionSaveReqVO reqVO) {
        // 校验考题是否存在
        validateQuestionExists(reqVO.getId());

        // 获取更新前的考题信息 - 用于操作日志的字段对比
        QuestionDO oldQuestion = questionMapper.selectById(reqVO.getId());
        if (oldQuestion == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }

        // 更新考题基本信息
        QuestionDO question = QuestionConvert.INSTANCE.convert(reqVO);
        questionMapper.updateById(question);

        // 删除原有选项
        questionOptionMapper.deleteByQuestionId(reqVO.getId());

        // 插入新选项
        if (reqVO.getOptions() != null && !reqVO.getOptions().isEmpty()) {
            List<QuestionOptionDO> options = QuestionConvert.INSTANCE.convertOptionVOListToDO(reqVO.getOptions());
            options.forEach(option -> {
                option.setQuestionId(reqVO.getId());
                option.setTenantId(TenantContextHolder.getTenantId()); // 设置租户ID
            });
            questionOptionMapper.insertBatch(options);
        }

        // 记录操作日志上下文 - 用于 _DIFF 函数的字段对比
        // 将旧的考题DO转换为相同的VO格式，确保字段对比的准确性
        QuestionSaveReqVO oldQuestionVO = QuestionConvert.INSTANCE.convertToSaveReqVO(oldQuestion);
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, oldQuestionVO);
        LogRecordContext.putVariable("question", oldQuestion);
        LogRecordContext.putVariable("updateReqVO", reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = QUESTION_TYPE, subType = QUESTION_DELETE_SUB_TYPE, bizNo = "{{#question.id}}", success = QUESTION_DELETE_SUCCESS)
    public void deleteQuestion(Long id) {
        // 校验考题是否存在
        validateQuestionExists(id);

        // 获取删除前的考题信息 - 用于操作日志记录
        QuestionDO question = questionMapper.selectById(id);
        if (question == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }

        // 软删除考题
        questionMapper.deleteById(id);

        // 删除选项
        questionOptionMapper.deleteByQuestionId(id);

        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("question", question);
    }

    @Override
    public QuestionStatisticsRespVO getQuestionStatistics(String biz, String level1Name, String level2Name, String level3Name) {
        // 查询统计数据
        List<QuestionDO> questions = questionMapper.selectStatistics(biz, level1Name, level2Name, level3Name);

        // 统计各题型数量
        QuestionStatisticsRespVO statistics = new QuestionStatisticsRespVO();
        statistics.setTotal(questions.size());

        Map<String, Long> typeCount = questions.stream()
                .collect(Collectors.groupingBy(QuestionDO::getType, Collectors.counting()));

        statistics.setSingle(typeCount.getOrDefault("单选题", 0L).intValue() + 
                           typeCount.getOrDefault("多选题", 0L).intValue());
        statistics.setJudge(typeCount.getOrDefault("判断题", 0L).intValue());
        statistics.setShortAnswer(typeCount.getOrDefault("简答题", 0L).intValue());
        statistics.setFill(typeCount.getOrDefault("填空题", 0L).intValue());
        statistics.setMaterial(typeCount.getOrDefault("材料题", 0L).intValue());
        statistics.setSort(typeCount.getOrDefault("排序题", 0L).intValue());
        statistics.setMatch(typeCount.getOrDefault("匹配题", 0L).intValue());
        statistics.setUpload(typeCount.getOrDefault("文件上传题", 0L).intValue());

        return statistics;
    }

    @Override
    public QuestionDO getQuestionDO(Long id) {
        return questionMapper.selectById(id);
    }

    @Override
    public List<QuestionDO> getQuestionDOList(Collection<Long> ids) {
        return questionMapper.selectBatchIds(ids);
    }

    @Override
    public void validateQuestionExists(Long id) {
        if (questionMapper.selectById(id) == null) {
            throw exception(QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public QuestionImportValidateRespVO validateImportExcelQuestions(List<QuestionImportExcelVO> excelList) {
        QuestionImportValidateRespVO respVO = new QuestionImportValidateRespVO();
        List<QuestionImportValidateItemVO> validateList = new ArrayList<>();

        int totalCount = excelList.size();
        int validCount = 0;
        int errorCount = 0;

        for (int i = 0; i < excelList.size(); i++) {
            QuestionImportExcelVO excelVO = excelList.get(i);
            QuestionImportValidateItemVO itemVO = new QuestionImportValidateItemVO();

            // 设置基本信息
            itemVO.setRowNum(i + 1);
            itemVO.setLevel1Name(excelVO.getLevel1Name());
            itemVO.setLevel1Code(excelVO.getLevel1Code());
            itemVO.setLevel2Name(excelVO.getLevel2Name());
            itemVO.setLevel2Code(excelVO.getLevel2Code());
            itemVO.setLevel3Name(excelVO.getLevel3Name());
            itemVO.setLevel3Code(excelVO.getLevel3Code());
            itemVO.setCertName(excelVO.getCertName());
            itemVO.setCertCode(excelVO.getCertCode());
            itemVO.setType(excelVO.getType());
            itemVO.setTitle(excelVO.getTitle());
            itemVO.setOptions(excelVO.getOptions());
            itemVO.setAnswer(excelVO.getAnswer());

            // 校验数据并转换（避免重复查询分类数据）
            ValidationResult validationResult = validateAndConvertImportQuestion(excelVO, i + 1);

            if (validationResult.isValid()) {
                itemVO.setValidateStatus("VALID");
                itemVO.setErrorMessage("");
                itemVO.setOriginalData(validationResult.getQuestionSaveReqVO());
                validCount++;
            } else {
                itemVO.setValidateStatus("ERROR");
                itemVO.setErrorMessage(validationResult.getErrorMessage());
                itemVO.setOriginalData(null);
                errorCount++;
            }

            validateList.add(itemVO);
        }

        respVO.setValidateList(validateList);
        respVO.setTotalCount(totalCount);
        respVO.setValidCount(validCount);
        respVO.setErrorCount(errorCount);

        return respVO;
    }

    @Override
    public QuestionImportRespVO importValidQuestions(List<QuestionSaveReqVO> questionList) {
        QuestionImportRespVO respVO = new QuestionImportRespVO();
        List<QuestionImportRespVO.QuestionImportFailItemVO> failList = new ArrayList<>();

        int totalCount = questionList.size();
        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < questionList.size(); i++) {
            QuestionSaveReqVO reqVO = questionList.get(i);
            try {
                createQuestion(reqVO);
                successCount++;
            } catch (Exception e) {
                failCount++;
                QuestionImportRespVO.QuestionImportFailItemVO failItem =
                    new QuestionImportRespVO.QuestionImportFailItemVO();
                failItem.setRowNum(i + 1);
                failItem.setTitle(reqVO.getTitle());
                failItem.setReason("导入失败：" + e.getMessage());
                failList.add(failItem);
                log.error("导入考题失败，行号：{}，题干：{}", i + 1, reqVO.getTitle(), e);
            }
        }

        respVO.setTotalCount(totalCount);
        respVO.setSuccessCount(successCount);
        respVO.setFailCount(failCount);
        respVO.setFailList(failList);

        return respVO;
    }

    /**
     * 转换Excel数据为QuestionSaveReqVO
     */
    private QuestionSaveReqVO convertToQuestionSaveReqVO(QuestionImportExcelVO excelVO) {
        QuestionSaveReqVO reqVO = new QuestionSaveReqVO();

        // 设置分类信息
        reqVO.setLevel1Name(trim(excelVO.getLevel1Name()));
        reqVO.setLevel1Code(trim(excelVO.getLevel1Code()));
        reqVO.setLevel2Name(trim(excelVO.getLevel2Name()));
        reqVO.setLevel2Code(trim(excelVO.getLevel2Code()));
        reqVO.setLevel3Name(trim(excelVO.getLevel3Name()));
        reqVO.setLevel3Code(trim(excelVO.getLevel3Code()));
        reqVO.setCertName(trim(excelVO.getCertName()));
        reqVO.setCertCode(trim(excelVO.getCertCode()));

        // 设置考题基本信息
        reqVO.setType(trim(excelVO.getType()));
        reqVO.setTitle(trim(excelVO.getTitle()));
        reqVO.setAnswer(trim(excelVO.getAnswer()));

        // 查询分类数据获取biz和bizName
        QuestionCategoryDO category = questionCategoryMapper.selectByNameAndCode(
                trim(excelVO.getLevel1Name()), trim(excelVO.getLevel1Code()),
                trim(excelVO.getLevel2Name()), trim(excelVO.getLevel2Code()),
                trim(excelVO.getLevel3Name()), trim(excelVO.getLevel3Code()),
                trim(excelVO.getCertName()), trim(excelVO.getCertCode())
        );

        // 从分类数据中获取biz和bizName，如果分类不存在则保持为空
        if (category != null) {
            reqVO.setBiz(category.getBiz());
            reqVO.setBizName(category.getBizName());
        }

        // 设置其他默认值
        reqVO.setDifficulty(1); // 默认难度为1
        reqVO.setScore(new BigDecimal("1.0")); // 默认分值为1分
        reqVO.setTimeLimit(0); // 默认无时间限制
        reqVO.setStatus(1); // 默认启用状态

        // 解析选项
        if (!isBlank(excelVO.getOptions())) {
            List<QuestionOptionVO> options = parseOptions(excelVO.getOptions(), excelVO.getType());
            reqVO.setOptions(options);
        }

        return reqVO;
    }

    /**
     * 转换Excel数据为QuestionSaveReqVO（使用已查询的分类数据）
     */
    private QuestionSaveReqVO convertToQuestionSaveReqVOWithCategory(QuestionImportExcelVO excelVO, QuestionCategoryDO category) {
        QuestionSaveReqVO reqVO = new QuestionSaveReqVO();

        // 设置分类信息
        reqVO.setLevel1Name(trim(excelVO.getLevel1Name()));
        reqVO.setLevel1Code(trim(excelVO.getLevel1Code()));
        reqVO.setLevel2Name(trim(excelVO.getLevel2Name()));
        reqVO.setLevel2Code(trim(excelVO.getLevel2Code()));
        reqVO.setLevel3Name(trim(excelVO.getLevel3Name()));
        reqVO.setLevel3Code(trim(excelVO.getLevel3Code()));
        reqVO.setCertName(trim(excelVO.getCertName()));
        reqVO.setCertCode(trim(excelVO.getCertCode()));

        // 设置考题基本信息
        reqVO.setType(trim(excelVO.getType()));
        reqVO.setTitle(trim(excelVO.getTitle()));
        reqVO.setAnswer(trim(excelVO.getAnswer()));

        // 从分类数据中获取biz和bizName
        reqVO.setBiz(category.getBiz());
        reqVO.setBizName(category.getBizName());

        // 设置其他默认值
        reqVO.setDifficulty(1); // 默认难度为1
        reqVO.setScore(new BigDecimal("1.0")); // 默认分值为1分
        reqVO.setTimeLimit(0); // 默认无时间限制
        reqVO.setStatus(1); // 默认启用状态

        // 解析选项
        if ("匹配题".equals(excelVO.getType())) {
            // 匹配题需要解析左右选项
            if (!isBlank(excelVO.getOptions()) && !isBlank(excelVO.getRightOptions())) {
                List<QuestionOptionVO> options = parseMatchOptions(excelVO.getOptions(), excelVO.getRightOptions());
                reqVO.setOptions(options);
            }
        } else {
            // 其他题型解析普通选项
            if (!isBlank(excelVO.getOptions())) {
                List<QuestionOptionVO> options = parseOptions(excelVO.getOptions(), excelVO.getType());
                reqVO.setOptions(options);
            }
        }

        return reqVO;
    }

    /**
     * 解析选择项字符串为选项列表
     */
    private List<QuestionOptionVO> parseOptions(String optionsStr, String type) {
        List<QuestionOptionVO> options = new ArrayList<>();

        if (isBlank(optionsStr)) {
            return options;
        }

        // 按 | 分割选项
        String[] optionArray = optionsStr.split("\\|");

        for (int i = 0; i < optionArray.length; i++) {
            String optionText = optionArray[i].trim();
            if (isBlank(optionText)) {
                continue;
            }

            QuestionOptionVO option = new QuestionOptionVO();
            option.setOptionType("choice");
            option.setOptionKey(String.valueOf((char) ('A' + i))); // A, B, C, D...
            option.setOptionContent(optionText);
            option.setIsCorrect(false); // 默认不是正确答案，后续根据answer设置
            option.setSortOrder(i + 1);

            options.add(option);
        }

        return options;
    }

    /**
     * 解析匹配题的左右选项
     */
    private List<QuestionOptionVO> parseMatchOptions(String leftOptionsStr, String rightOptionsStr) {
        List<QuestionOptionVO> options = new ArrayList<>();

        if (isBlank(leftOptionsStr) || isBlank(rightOptionsStr)) {
            return options;
        }

        // 解析左侧选项
        String[] leftOptionArray = leftOptionsStr.split("\\|");
        for (int i = 0; i < leftOptionArray.length; i++) {
            String optionText = leftOptionArray[i].trim();
            if (isBlank(optionText)) {
                continue;
            }

            QuestionOptionVO option = new QuestionOptionVO();
            option.setOptionType("matchLeft");
            option.setOptionKey(String.valueOf(i + 1)); // 1, 2, 3, 4...
            option.setOptionContent(optionText);
            option.setIsCorrect(false);
            option.setSortOrder(i + 1);

            options.add(option);
        }

        // 解析右侧选项
        String[] rightOptionArray = rightOptionsStr.split("\\|");
        for (int i = 0; i < rightOptionArray.length; i++) {
            String optionText = rightOptionArray[i].trim();
            if (isBlank(optionText)) {
                continue;
            }

            QuestionOptionVO option = new QuestionOptionVO();
            option.setOptionType("matchRight");
            option.setOptionKey(String.valueOf((char) ('A' + i))); // A, B, C, D...
            option.setOptionContent(optionText);
            option.setIsCorrect(false);
            option.setSortOrder(i + 1);

            options.add(option);
        }

        return options;
    }

    /**
     * 字符串去空格处理
     */
    private String trim(String str) {
        return str == null ? null : str.trim();
    }

    /**
     * 校验并转换导入的考题数据（避免重复查询分类数据）
     */
    private ValidationResult validateAndConvertImportQuestion(QuestionImportExcelVO excelVO, int rowNum) {
        // 1. 必填字段验证
        if (isBlank(excelVO.getLevel1Name())) {
            return new ValidationResult(false, "第" + rowNum + "行：一级名称不能为空", null);
        }
        if (isBlank(excelVO.getLevel1Code())) {
            return new ValidationResult(false, "第" + rowNum + "行：一级代码不能为空", null);
        }
        if (isBlank(excelVO.getLevel2Name())) {
            return new ValidationResult(false, "第" + rowNum + "行：二级名称不能为空", null);
        }
        if (isBlank(excelVO.getLevel2Code())) {
            return new ValidationResult(false, "第" + rowNum + "行：二级代码不能为空", null);
        }
        if (isBlank(excelVO.getLevel3Name())) {
            return new ValidationResult(false, "第" + rowNum + "行：三级名称不能为空", null);
        }
        if (isBlank(excelVO.getLevel3Code())) {
            return new ValidationResult(false, "第" + rowNum + "行：三级代码不能为空", null);
        }
        if (isBlank(excelVO.getCertName())) {
            return new ValidationResult(false, "第" + rowNum + "行：认定点名称不能为空", null);
        }
        if (isBlank(excelVO.getCertCode())) {
            return new ValidationResult(false, "第" + rowNum + "行：认定点代码不能为空", null);
        }
        if (isBlank(excelVO.getType())) {
            return new ValidationResult(false, "第" + rowNum + "行：题型不能为空", null);
        }
        if (isBlank(excelVO.getTitle())) {
            return new ValidationResult(false, "第" + rowNum + "行：题干不能为空", null);
        }
        if (isBlank(excelVO.getAnswer())) {
            return new ValidationResult(false, "第" + rowNum + "行：参考答案不能为空", null);
        }

        // 2. 题型枚举值验证
        String type = excelVO.getType().trim();
        if (!cn.bztmaster.cnt.module.publicbiz.enums.QuestionTypeEnum.isValid(type)) {
            return new ValidationResult(false, "第" + rowNum + "行：题型[" + type + "]不正确，必须是题型枚举值之一", null);
        }

        // 3. 题型相关验证
        if ("单选题".equals(type) || "多选题".equals(type) || "判断题".equals(type) || "排序题".equals(type)) {
            if (isBlank(excelVO.getOptions())) {
                return new ValidationResult(false, "第" + rowNum + "行：" + type + "题的选择项不能为空", null);
            }
        }

        // 匹配题特殊验证
        if ("匹配题".equals(type)) {
            if (isBlank(excelVO.getOptions())) {
                return new ValidationResult(false, "第" + rowNum + "行：匹配题的选择项不能为空", null);
            }
            if (isBlank(excelVO.getRightOptions())) {
                return new ValidationResult(false, "第" + rowNum + "行：匹配题的右选择项不能为空", null);
            }
        }

        // 4. 数据库关联验证（只查询一次）
        QuestionCategoryDO category = questionCategoryMapper.selectByNameAndCode(
                excelVO.getLevel1Name().trim(), excelVO.getLevel1Code().trim(),
                excelVO.getLevel2Name().trim(), excelVO.getLevel2Code().trim(),
                excelVO.getLevel3Name().trim(), excelVO.getLevel3Code().trim(),
                excelVO.getCertName().trim(), excelVO.getCertCode().trim()
        );

        if (category == null) {
            return new ValidationResult(false, "第" + rowNum + "行：找不到对应的考题分类，请检查分类名称和代码是否正确", null);
        }

        // 5. 校验通过，转换为QuestionSaveReqVO
        QuestionSaveReqVO reqVO = convertToQuestionSaveReqVOWithCategory(excelVO, category);
        return new ValidationResult(true, null, reqVO);
    }

    /**
     * 判断字符串是否为空
     */
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 校验结果内部类
     */
    private static class ValidationResult {
        private boolean valid;
        private String errorMessage;
        private QuestionSaveReqVO questionSaveReqVO;

        public ValidationResult(boolean valid, String errorMessage, QuestionSaveReqVO questionSaveReqVO) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.questionSaveReqVO = questionSaveReqVO;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public QuestionSaveReqVO getQuestionSaveReqVO() {
            return questionSaveReqVO;
        }
    }

}
