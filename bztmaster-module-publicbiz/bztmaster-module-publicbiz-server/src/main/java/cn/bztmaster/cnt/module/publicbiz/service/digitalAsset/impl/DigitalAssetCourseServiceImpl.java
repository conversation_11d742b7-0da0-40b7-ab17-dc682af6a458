package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset.DigitalAssetCourseConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.DigitalAssetCourseDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.DigitalAssetCourseMapper;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.DigitalAssetCourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 数字资产课程 Service 实现类
 * 
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DigitalAssetCourseServiceImpl implements DigitalAssetCourseService {

    @Resource
    private DigitalAssetCourseMapper digitalAssetCourseMapper;

    @Override
    public List<DigitalAssetCourseRespVO> listDigitalAssetCourse(DigitalAssetCourseListReqVO reqVO) {
        List<DigitalAssetCourseDO> list = digitalAssetCourseMapper.selectList(reqVO);
        return DigitalAssetCourseConvert.INSTANCE.convertList(list);
    }

    @Override
    public PageResult<DigitalAssetCourseRespVO> pageDigitalAssetCourse(DigitalAssetCoursePageReqVO reqVO) {
        PageResult<DigitalAssetCourseDO> pageResult = digitalAssetCourseMapper.selectPage(reqVO);
        return DigitalAssetCourseConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public Long createDigitalAssetCourse(DigitalAssetCourseSaveReqVO reqVO) {
        // 转换并插入
        DigitalAssetCourseDO course = DigitalAssetCourseConvert.INSTANCE.convert(reqVO);
        digitalAssetCourseMapper.insert(course);
        return course.getId();
    }

    @Override
    public void updateDigitalAssetCourse(DigitalAssetCourseSaveReqVO reqVO) {
        // 校验存在
        validateCourseExists(reqVO.getId());
        // 转换并更新
        DigitalAssetCourseDO updateObj = DigitalAssetCourseConvert.INSTANCE.convert(reqVO);
        digitalAssetCourseMapper.updateById(updateObj);
    }

    @Override
    public void deleteDigitalAssetCourse(Long id) {
        // 校验存在
        validateCourseExists(id);
        // 删除
        digitalAssetCourseMapper.deleteById(id);
    }

    @Override
    public DigitalAssetCourseRespVO getDigitalAssetCourse(Long id) {
        DigitalAssetCourseDO course = digitalAssetCourseMapper.selectById(id);
        return DigitalAssetCourseConvert.INSTANCE.convert(course);
    }

    @Override
    public void updateDigitalAssetCourseStatus(Long id, String status) {
        // 校验存在
        validateCourseExists(id);
        // 更新状态
        DigitalAssetCourseDO updateObj = new DigitalAssetCourseDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        digitalAssetCourseMapper.updateById(updateObj);
    }

    @Override
    public CourseStatisticsRespVO getCourseStatistics() {
        return digitalAssetCourseMapper.selectStatistics();
    }

    @Override
    public DigitalAssetCourseDO getCourse(Long id) {
        return digitalAssetCourseMapper.selectById(id);
    }

    @Override
    public List<DigitalAssetCourseDO> getCourseList(Collection<Long> ids) {
        return digitalAssetCourseMapper.selectBatchIds(ids);
    }

    private void validateCourseExists(Long id) {
        if (digitalAssetCourseMapper.selectById(id) == null) {
            throw exception(DIGITAL_ASSET_COURSE_NOT_EXISTS);
        }
    }
}
