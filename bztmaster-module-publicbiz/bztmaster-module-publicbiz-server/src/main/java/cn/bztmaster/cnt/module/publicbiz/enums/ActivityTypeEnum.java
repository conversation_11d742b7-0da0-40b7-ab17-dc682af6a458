package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 活动类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ActivityTypeEnum implements ArrayValuable<String> {

    TRAINING("培训", "技能培训、教学活动"),
    EXAM("考试", "各类考试、测评"),
    MEETING("会议", "会议、座谈"),
    LECTURE("讲座", "讲座、宣讲"),
    OTHER("其他", "其他类型活动");

    public static final String[] ARRAYS = Arrays.stream(values()).map(ActivityTypeEnum::getType).toArray(String[]::new);

    /**
     * 类型值
     */
    private final String type;
    /**
     * 类型描述
     */
    private final String description;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 枚举
     */
    public static ActivityTypeEnum getByType(String type) {
        return Arrays.stream(values())
                .filter(item -> item.getType().equals(type))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的活动类型
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValid(String type) {
        return getByType(type) != null;
    }
}
