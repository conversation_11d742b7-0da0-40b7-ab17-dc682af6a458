package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@TableName("publicbiz_practitioner_qualification")
@Schema(description = "阿姨资质文件表 DO")
public class PractitionerQualificationDO {
    @TableId
    private Long id;
    private Long practitionerId;
    private String fileType;
    private String fileName;
    private String fileUrl;
    private Long fileSize;
    private String fileExtension;
    private Integer sortOrder;
    private Integer status;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 