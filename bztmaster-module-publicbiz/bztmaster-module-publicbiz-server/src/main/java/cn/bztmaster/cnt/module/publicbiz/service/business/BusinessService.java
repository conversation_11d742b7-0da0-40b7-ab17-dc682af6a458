package cn.bztmaster.cnt.module.publicbiz.service.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;

import java.util.Collection;
import java.util.List;

public interface BusinessService {
    PageResult<BusinessRespVO> getBusinessPage(BusinessPageReqVO reqVO);
    Long createBusiness(BusinessSaveReqVO reqVO);
    void updateBusiness(BusinessSaveReqVO reqVO);
    void deleteBusiness(Long id);
    BusinessDetailRespVO getBusinessDetail(Long id);
    
    // API接口需要的方法
    BusinessDO getBusiness(Long id);
    List<BusinessDO> getBusinessList(Collection<Long> ids);
} 