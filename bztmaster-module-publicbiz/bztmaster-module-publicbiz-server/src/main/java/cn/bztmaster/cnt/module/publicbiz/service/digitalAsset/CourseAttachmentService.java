package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import java.util.List;

/**
 * 课程附件 Service 接口
 * 
 * <AUTHOR>
 */
public interface CourseAttachmentService {

    /**
     * 获取课程附件列表
     * 
     * @param courseId 课程ID
     * @return 附件列表
     */
    List<CourseAttachmentRespVO> listCourseAttachment(Long courseId);

    /**
     * 添加课程附件
     * 
     * @param reqVO 附件信息
     * @return 附件ID
     */
    Long addCourseAttachment(CourseAttachmentSaveReqVO reqVO);

    /**
     * 移除课程附件
     * 
     * @param id 附件ID
     */
    void removeCourseAttachment(Long id);
}
