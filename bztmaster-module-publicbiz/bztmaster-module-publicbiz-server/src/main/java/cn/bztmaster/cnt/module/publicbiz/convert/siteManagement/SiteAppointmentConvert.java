package cn.bztmaster.cnt.module.publicbiz.convert.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteAppointmentRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.SiteAppointmentSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteAppointmentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 场地预约 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SiteAppointmentConvert {

    SiteAppointmentConvert INSTANCE = Mappers.getMapper(SiteAppointmentConvert.class);

    // ========== DO 转换 ==========

    @Mapping(target = "timeRange", expression = "java(generateTimeRange(bean.getStartTime(), bean.getEndTime()))")
    SiteAppointmentRespVO convert(SiteAppointmentDO bean);

    @Mapping(target = "timeRange", expression = "java(generateTimeRange(bean.getStartTime(), bean.getEndTime()))")
    SiteAppointmentDO convert(SiteAppointmentSaveReqVO bean);

    List<SiteAppointmentRespVO> convertList(List<SiteAppointmentDO> list);

    PageResult<SiteAppointmentRespVO> convertPage(PageResult<SiteAppointmentDO> page);

    // ========== 带场地信息的转换 ==========

    /**
     * 转换预约信息，并设置场地校区名称
     */
    default SiteAppointmentRespVO convertWithSiteInfo(SiteAppointmentDO appointment, String siteCampusName) {
        SiteAppointmentRespVO respVO = convert(appointment);
        if (respVO != null) {
            respVO.setSiteCampusName(siteCampusName);
        }
        return respVO;
    }

    // ========== 辅助方法 ==========

    /**
     * 生成时间段描述
     */
    default String generateTimeRange(LocalTime startTime, LocalTime endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return startTime.format(formatter) + "-" + endTime.format(formatter);
    }

}
