package cn.bztmaster.cnt.module.publicbiz.service.certificateTemplate.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplatePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.convert.certificateTemplate.CertificateTemplateConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateFieldDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.certificateTemplate.CertificateTemplateMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.certificateTemplate.CertificateTemplateFieldMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.certificateTemplate.CertificateTemplateStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.certificateTemplate.CertificateTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 证书模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CertificateTemplateServiceImpl implements CertificateTemplateService {

    @Resource
    private CertificateTemplateMapper certificateTemplateMapper;

    @Resource
    private CertificateTemplateFieldMapper certificateTemplateFieldMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCertificateTemplate(CertificateTemplateSaveReqVO createReqVO) {
        // 校验模板名称唯一性
        validateCertificateTemplateNameUnique(null, createReqVO.getName());

        // 插入证书模板
        CertificateTemplateDO certificateTemplate = BeanUtils.toBean(createReqVO, CertificateTemplateDO.class);

        // 设置创建者姓名
        String creatorName = SecurityFrameworkUtils.getLoginUserNickname();
        if (creatorName != null) {
            certificateTemplate.setCreatorName(creatorName);
        }

        certificateTemplateMapper.insert(certificateTemplate);

        // 插入字段配置
        if (createReqVO.getFields() != null && !createReqVO.getFields().isEmpty()) {
            List<CertificateTemplateFieldDO> fieldDOList = CertificateTemplateConvert.INSTANCE.convertFieldListFromVO(createReqVO.getFields());
            fieldDOList.forEach(field -> {
                field.setTemplateId(certificateTemplate.getId());
                certificateTemplateFieldMapper.insert(field);
            });
        }

        // 返回
        return certificateTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCertificateTemplate(CertificateTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateCertificateTemplateExists(updateReqVO.getId());
        // 校验模板名称唯一性
        validateCertificateTemplateNameUnique(updateReqVO.getId(), updateReqVO.getName());

        // 更新证书模板
        CertificateTemplateDO updateObj = BeanUtils.toBean(updateReqVO, CertificateTemplateDO.class);
        certificateTemplateMapper.updateById(updateObj);

        // 删除旧的字段配置
        certificateTemplateFieldMapper.deleteByTemplateId(updateReqVO.getId());

        // 插入新的字段配置
        if (updateReqVO.getFields() != null && !updateReqVO.getFields().isEmpty()) {
            List<CertificateTemplateFieldDO> fieldDOList = CertificateTemplateConvert.INSTANCE.convertFieldListFromVO(updateReqVO.getFields());
            fieldDOList.forEach(field -> {
                field.setTemplateId(updateReqVO.getId());
                certificateTemplateFieldMapper.insert(field);
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCertificateTemplate(Long id) {
        // 校验存在
        validateCertificateTemplateExists(id);

        // 删除证书模板
        certificateTemplateMapper.deleteById(id);

        // 删除字段配置
        certificateTemplateFieldMapper.deleteByTemplateId(id);
    }

    private void validateCertificateTemplateNameUnique(Long id, String name) {
        CertificateTemplateDO certificateTemplate = certificateTemplateMapper.selectByName(name);
        if (certificateTemplate == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的模板
        if (id == null) {
            throw exception(CERTIFICATE_TEMPLATE_NAME_DUPLICATE);
        }
        if (!certificateTemplate.getId().equals(id)) {
            throw exception(CERTIFICATE_TEMPLATE_NAME_DUPLICATE);
        }
    }

    @Override
    public void validateCertificateTemplateExists(Long id) {
        if (certificateTemplateMapper.selectById(id) == null) {
            throw exception(CERTIFICATE_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public CertificateTemplateDO getCertificateTemplate(Long id) {
        CertificateTemplateDO certificateTemplate = certificateTemplateMapper.selectById(id);
        if (certificateTemplate == null) {
            return null;
        }

        // 查询字段配置
        List<CertificateTemplateFieldDO> fields = certificateTemplateFieldMapper.selectListByTemplateId(id);
        // 将字段配置设置到模板对象中
        certificateTemplate.setFields(fields);

        return certificateTemplate;
    }

    @Override
    public PageResult<CertificateTemplateDO> getCertificateTemplatePage(CertificateTemplatePageReqVO pageReqVO) {
        return certificateTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public List<CertificateTemplateDO> getCertificateTemplateList(CertificateTemplateListReqVO listReqVO) {
        return certificateTemplateMapper.selectList(listReqVO.getStatus());
    }

    @Override
    public void updateCertificateTemplateStatus(Long id, String status) {
        // 校验存在
        validateCertificateTemplateExists(id);
        // 校验状态
        if (CertificateTemplateStatusEnum.getByCode(status) == null) {
            throw exception(CERTIFICATE_TEMPLATE_STATUS_INVALID);
        }

        // 更新状态
        CertificateTemplateDO updateObj = new CertificateTemplateDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        certificateTemplateMapper.updateById(updateObj);
    }

    @Override
    public CertificateTemplateStatisticsRespVO getCertificateTemplateStatistics() {
        CertificateTemplateStatisticsRespVO statistics = new CertificateTemplateStatisticsRespVO();

        // 统计总数
        Long total = certificateTemplateMapper.selectTotalCount();
        statistics.setTotal(total.intValue());

        // 统计各状态数量
        Long activeCount = certificateTemplateMapper.selectCountByStatus(CertificateTemplateStatusEnum.ACTIVE.getCode());
        statistics.setActiveCount(activeCount.intValue());

        Long inactiveCount = certificateTemplateMapper.selectCountByStatus(CertificateTemplateStatusEnum.INACTIVE.getCode());
        statistics.setInactiveCount(inactiveCount.intValue());

        Long draftCount = certificateTemplateMapper.selectCountByStatus(CertificateTemplateStatusEnum.DRAFT.getCode());
        statistics.setDraftCount(draftCount.intValue());

        return statistics;
    }

}