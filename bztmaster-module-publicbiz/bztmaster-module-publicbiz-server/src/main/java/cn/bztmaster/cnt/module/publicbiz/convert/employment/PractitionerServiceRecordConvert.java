package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerServiceRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PractitionerServiceRecordConvert {
    PractitionerServiceRecordConvert INSTANCE = Mappers.getMapper(PractitionerServiceRecordConvert.class);

    PractitionerServiceRecordRespVO convert(PractitionerServiceRecordDO bean);

    List<PractitionerServiceRecordRespVO> convertList(List<PractitionerServiceRecordDO> list);
} 