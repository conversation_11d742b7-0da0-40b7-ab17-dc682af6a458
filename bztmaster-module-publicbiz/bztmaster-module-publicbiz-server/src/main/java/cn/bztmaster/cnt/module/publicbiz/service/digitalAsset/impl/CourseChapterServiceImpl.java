package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset.CourseChapterConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset.CourseLessonConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseChapterDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.CourseLessonDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseChapterMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.digitalAsset.CourseLessonMapper;
import cn.bztmaster.cnt.module.publicbiz.service.digitalAsset.CourseChapterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 课程章节 Service 实现类
 * 
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CourseChapterServiceImpl implements CourseChapterService {

    @Resource
    private CourseChapterMapper courseChapterMapper;
    
    @Resource
    private CourseLessonMapper courseLessonMapper;

    @Override
    public List<CourseChapterRespVO> listCourseChapter(Long courseId) {
        // 查询章节列表
        List<CourseChapterDO> chapters = courseChapterMapper.selectListByCourseId(courseId);
        List<CourseChapterRespVO> chapterVOs = CourseChapterConvert.INSTANCE.convertList(chapters);
        
        // 为每个章节查询课时列表
        for (CourseChapterRespVO chapterVO : chapterVOs) {
            List<CourseLessonDO> lessons = courseLessonMapper.selectListByChapterId(chapterVO.getId());
            chapterVO.setLessons(CourseLessonConvert.INSTANCE.convertList(lessons));
        }
        
        return chapterVOs;
    }

    @Override
    public Long createCourseChapter(CourseChapterSaveReqVO reqVO) {
        // 转换并插入
        CourseChapterDO chapter = CourseChapterConvert.INSTANCE.convert(reqVO);
        courseChapterMapper.insert(chapter);
        return chapter.getId();
    }

    @Override
    public void updateCourseChapter(CourseChapterSaveReqVO reqVO) {
        // 校验存在
        validateChapterExists(reqVO.getId());
        // 转换并更新
        CourseChapterDO updateObj = CourseChapterConvert.INSTANCE.convert(reqVO);
        courseChapterMapper.updateById(updateObj);
    }

    @Override
    public void deleteCourseChapter(Long id) {
        // 校验存在
        validateChapterExists(id);
        // 删除
        courseChapterMapper.deleteById(id);
    }

    private void validateChapterExists(Long id) {
        if (courseChapterMapper.selectById(id) == null) {
            throw exception(COURSE_CHAPTER_NOT_EXISTS);
        }
    }
}
