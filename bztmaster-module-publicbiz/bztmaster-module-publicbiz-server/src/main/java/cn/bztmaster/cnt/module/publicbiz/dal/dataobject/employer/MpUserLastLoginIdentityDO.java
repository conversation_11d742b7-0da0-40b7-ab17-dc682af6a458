package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小程序用户最后登录身份记录表 DO
 *
 * <AUTHOR>
 */
@TableName("mp_user_last_login_identity")
@Data
@Schema(description = "小程序用户最后登录身份记录表")
public class MpUserLastLoginIdentityDO {

    /**
     * 主键ID
     */
    @TableId
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    /**
     * 微信openid
     */
    @Schema(description = "微信openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String openid;

    /**
     * 微信unionid
     */
    @Schema(description = "微信unionid", example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String unionid;

    /**
     * 身份类型：EMPLOYER-雇主，AUNT-阿姨，AGENCY-机构
     */
    @Schema(description = "身份类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    private String identityType;

    /**
     * 身份关联ID（如雇主ID、阿姨ID等）
     */
    @Schema(description = "身份关联ID", example = "1024")
    private Long identityId;

    /**
     * 身份名称（如雇主姓名、阿姨姓名等）
     */
    @Schema(description = "身份名称", example = "张先生")
    private String identityName;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息", example = "iPhone 14 Pro")
    private String deviceInfo;

    /**
     * 登录状态：1-正常，0-异常
     */
    @Schema(description = "登录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer loginStatus;

    /**
     * 微信session_key
     */
    @Schema(description = "微信session_key", example = "session_key_example")
    private String sessionKey;

    /**
     * 访问令牌
     */
    @Schema(description = "访问令牌", example = "access_token_example")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @Schema(description = "刷新令牌", example = "refresh_token_example")
    private String refreshToken;

    /**
     * 令牌过期时间
     */
    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpireTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    private String creator;

    /**
     * 更新者
     */
    @Schema(description = "更新者")
    private String updater;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Boolean deleted;

    /**
     * 租户编号
     */
    @Schema(description = "租户编号")
    private Long tenantId;

}
