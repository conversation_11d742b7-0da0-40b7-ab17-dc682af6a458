package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 线索跟进记录 Service 接口
 *
 * <AUTHOR>
 */
public interface LeadFollowUpLogService {

    /**
     * 创建线索跟进记录
     *
     * @param createReqDTO 创建线索跟进记录请求
     * @return 线索跟进记录编号
     */
    Long createLeadFollowUpLog(@Valid LeadFollowUpLogSaveReqDTO createReqDTO);

    /**
     * 更新线索跟进记录
     *
     * @param updateReqDTO 更新线索跟进记录请求
     */
    void updateLeadFollowUpLog(@Valid LeadFollowUpLogSaveReqDTO updateReqDTO);

    /**
     * 删除线索跟进记录
     *
     * @param id 线索跟进记录编号
     */
    void deleteLeadFollowUpLog(Long id);

    /**
     * 获取线索跟进记录
     *
     * @param id 线索跟进记录编号
     * @return 线索跟进记录
     */
    LeadFollowUpLogDO getLeadFollowUpLog(Long id);

    /**
     * 获取线索跟进记录列表
     *
     * @param leadId 线索ID
     * @return 线索跟进记录列表
     */
    List<LeadFollowUpLogDO> getLeadFollowUpLogListByLeadId(String leadId);

    /**
     * 获取线索跟进记录分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 线索跟进记录分页
     */
    PageResult<LeadFollowUpLogDO> getLeadFollowUpLogPage(LeadFollowUpLogPageReqDTO pageReqDTO);

    /**
     * 根据线索ID删除跟进记录
     *
     * @param leadId 线索ID
     */
    void deleteLeadFollowUpLogByLeadId(String leadId);
}