package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考题选项表 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_question_option")
@Schema(description = "考题选项表 DO")
public class QuestionOptionDO extends BaseDO {

    /**
     * 主键，自增
     */
    @TableId
    @Schema(description = "主键，自增")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 考题ID，关联publicbiz_question表
     */
    @Schema(description = "考题ID")
    private Long questionId;

    /**
     * 选项类型：choice-选择项，match_left-匹配左列，match_right-匹配右列
     */
    @Schema(description = "选项类型")
    private String optionType;

    /**
     * 选项标识，如：A、B、C、D或1、2、3、4
     */
    @Schema(description = "选项标识")
    private String optionKey;

    /**
     * 选项内容
     */
    @Schema(description = "选项内容")
    private String optionContent;

    /**
     * 是否正确答案：0-否，1-是
     */
    @Schema(description = "是否正确答案")
    private Boolean isCorrect;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     * 匹配目标，用于匹配题记录对应关系
     */
    @Schema(description = "匹配目标")
    private String matchTarget;

}
