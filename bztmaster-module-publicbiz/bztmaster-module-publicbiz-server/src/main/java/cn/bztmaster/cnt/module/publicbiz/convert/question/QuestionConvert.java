package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionOptionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 考题转换类
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface QuestionConvert {

    QuestionConvert INSTANCE = Mappers.getMapper(QuestionConvert.class);

    /**
     * 保存请求VO转换为DO
     *
     * @param bean 保存请求VO
     * @return DO
     */
    QuestionDO convert(QuestionSaveReqVO bean);

    /**
     * DO转换为响应VO
     *
     * @param bean DO
     * @return 响应VO
     */
    QuestionRespVO convert(QuestionDO bean);

    /**
     * DO转换为保存请求VO（用于操作日志的字段对比）
     *
     * @param bean DO
     * @return 保存请求VO
     */
    QuestionSaveReqVO convertToSaveReqVO(QuestionDO bean);

    /**
     * DO列表转换为响应VO列表
     *
     * @param list DO列表
     * @return 响应VO列表
     */
    List<QuestionRespVO> convertList(List<QuestionDO> list);

    /**
     * 分页结果转换
     *
     * @param page 分页结果
     * @return 响应VO分页结果
     */
    PageResult<QuestionRespVO> convertPage(PageResult<QuestionDO> page);

    /**
     * 选项VO转换为选项DO
     *
     * @param bean 选项VO
     * @return 选项DO
     */
    QuestionOptionDO convertOptionVOToDO(QuestionOptionVO bean);

    /**
     * 选项DO转换为选项VO
     *
     * @param bean 选项DO
     * @return 选项VO
     */
    QuestionOptionVO convertOptionDOToVO(QuestionOptionDO bean);

    /**
     * 选项VO列表转换为选项DO列表
     *
     * @param list 选项VO列表
     * @return 选项DO列表
     */
    List<QuestionOptionDO> convertOptionVOListToDO(List<QuestionOptionVO> list);

    /**
     * 选项DO列表转换为选项VO列表
     *
     * @param list 选项DO列表
     * @return 选项VO列表
     */
    List<QuestionOptionVO> convertOptionDOListToVO(List<QuestionOptionDO> list);

}
