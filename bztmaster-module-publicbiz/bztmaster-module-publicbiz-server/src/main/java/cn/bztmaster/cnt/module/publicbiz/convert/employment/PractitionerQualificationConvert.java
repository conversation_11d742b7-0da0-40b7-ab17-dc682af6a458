package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerQualificationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PractitionerQualificationConvert {
    PractitionerQualificationConvert INSTANCE = Mappers.getMapper(PractitionerQualificationConvert.class);

    PractitionerQualificationDO convert(PractitionerQualificationSaveReqVO bean);

    PractitionerQualificationRespVO convert(PractitionerQualificationDO bean);

    List<PractitionerQualificationRespVO> convertList(List<PractitionerQualificationDO> list);
} 