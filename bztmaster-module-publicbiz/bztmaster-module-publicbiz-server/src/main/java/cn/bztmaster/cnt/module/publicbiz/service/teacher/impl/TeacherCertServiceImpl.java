package cn.bztmaster.cnt.module.publicbiz.service.teacher.impl;

import cn.bztmaster.cnt.module.publicbiz.service.teacher.TeacherCertService;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.teacher.TeacherCertMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherCertDO;
import cn.bztmaster.cnt.module.publicbiz.convert.teacher.TeacherCertConvert;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mzt.logapi.context.LogRecordContext;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;

@Service
public class TeacherCertServiceImpl implements TeacherCertService {
    @Resource
    private TeacherCertMapper teacherCertMapper;
    @Resource
    private TeacherCertConvert teacherCertConvert;

    /**
     * 新增讲师资质
     * 
     * 操作日志记录：
     * - 记录新增操作，包含讲师ID和资质名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param reqVO 新增请求参数
     * @return 资质ID
     */
    @Override
    @Transactional
    @LogRecord(type = LogRecordConstants.TEACHER_CERT_TYPE, subType = LogRecordConstants.TEACHER_CERT_CREATE_SUB_TYPE, bizNo = "{{#cert.id}}", success = LogRecordConstants.TEACHER_CERT_CREATE_SUCCESS)
    public Long createTeacherCert(TeacherCertSaveReqVO reqVO) {
        TeacherCertDO cert = teacherCertConvert.convert(reqVO);
        cert.setCreateTime(new Date());
        cert.setDeleted(false);
        cert.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        cert.setTenantId(
                SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
        teacherCertMapper.insert(cert);
        
        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("cert", cert);
        
        return cert.getId();
    }

    /**
     * 删除讲师资质（逻辑删除）
     * 
     * 操作日志记录：
     * - 记录删除操作，包含讲师ID和资质名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param id 资质ID
     */
    @Override
    @Transactional
    @LogRecord(type = LogRecordConstants.TEACHER_CERT_TYPE, subType = LogRecordConstants.TEACHER_CERT_DELETE_SUB_TYPE, bizNo = "{{#cert.id}}", success = LogRecordConstants.TEACHER_CERT_DELETE_SUCCESS)
    public void deleteTeacherCert(Long id) {
        // 参数校验
        if (id == null) {
            throw new IllegalArgumentException("删除参数不能为空");
        }

        TeacherCertDO cert = teacherCertMapper.selectById(id);
        if (cert == null) {
            throw new IllegalArgumentException("讲师资质不存在");
        }

        if (!Boolean.TRUE.equals(cert.getDeleted())) {
            cert.setDeleted(true);
            cert.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            teacherCertMapper.updateById(cert);
            
            // 记录操作日志上下文 - 用于日志模板中的变量替换
            LogRecordContext.putVariable("cert", cert);
        }
    }

    @Override
    public List<TeacherCertRespVO> getTeacherCertList(Long teacherId) {
        List<TeacherCertDO> certs = teacherCertMapper.selectList(
            new LambdaQueryWrapperX<TeacherCertDO>()
                .eq(TeacherCertDO::getTeacherId, teacherId)
                .eq(TeacherCertDO::getDeleted, false));
        return teacherCertConvert.convertList(certs);
    }
} 