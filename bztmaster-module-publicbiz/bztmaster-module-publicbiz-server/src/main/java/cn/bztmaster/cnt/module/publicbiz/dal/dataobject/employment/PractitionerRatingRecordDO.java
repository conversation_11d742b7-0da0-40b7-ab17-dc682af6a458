package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_practitioner_rating_record")
@Schema(description = "阿姨评级记录表 DO")
public class PractitionerRatingRecordDO {
    @TableId
    private Long id;
    private Long practitionerId;
    private String ratingType;
    private BigDecimal oldRating;
    private BigDecimal newRating;
    private BigDecimal ratingChange;
    private String ratingReason;
    private Long evaluatorId;
    private String evaluatorName;
    private String evaluatorType;
    private String relatedOrderId;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 