package cn.bztmaster.cnt.module.publicbiz.service.teacher.impl;

import cn.bztmaster.cnt.module.publicbiz.service.teacher.TeacherService;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.teacher.TeacherMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.teacher.TeacherCertMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherCertDO;
import cn.bztmaster.cnt.module.publicbiz.convert.teacher.TeacherConvert;
import cn.bztmaster.cnt.module.publicbiz.convert.teacher.TeacherCertConvert;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportValidateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.TeacherImportValidateItemVO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;

@Service
public class TeacherServiceImpl implements TeacherService {
    @Resource
    private TeacherMapper teacherMapper;
    @Resource
    private TeacherCertMapper teacherCertMapper;
    @Resource
    private TeacherConvert teacherConvert;
    @Resource
    private TeacherCertConvert teacherCertConvert;

    /**
     * 新增讲师
     * 
     * 操作日志记录：
     * - 记录新增操作，包含讲师姓名
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param reqVO 新增请求参数
     * @return 讲师ID
     */
    @Override
    @Transactional
    @LogRecord(type = LogRecordConstants.TEACHER_TYPE, subType = LogRecordConstants.TEACHER_CREATE_SUB_TYPE, bizNo = "{{#teacher.id}}", success = LogRecordConstants.TEACHER_CREATE_SUCCESS)
    public Long createTeacher(TeacherSaveReqVO reqVO) {
        // 1. 保存主表
        TeacherDO teacher = teacherConvert.convert(reqVO);
        teacher.setCreateTime(new Date());
        teacher.setDeleted(false);
        teacher.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        teacher.setTenantId(
                SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);
        teacherMapper.insert(teacher);
        // 2. 保存资质（新增时所有资质文件都是新增）
        if (CollUtil.isNotEmpty(reqVO.getCertFiles())) {
            for (TeacherCertSaveReqVO certVO : reqVO.getCertFiles()) {
                TeacherCertDO cert = teacherCertConvert.convert(certVO);
                cert.setTeacherId(teacher.getId());
                cert.setCreateTime(new Date());
                cert.setDeleted(false);
                cert.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                teacherCertMapper.insert(cert);
            }
        }
        
        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("teacher", teacher);
        
        return teacher.getId();
    }

    /**
     * 更新讲师
     * 
     * 操作日志记录：
     * - 记录更新操作，包含讲师姓名
     * - 使用 _DIFF 函数记录字段级别的变更
     * - 通过 LogRecordContext.putVariable 设置新旧对象用于对比
     * 
     * @param reqVO 更新请求参数
     */
    @Override
    @Transactional
    @LogRecord(type = LogRecordConstants.TEACHER_TYPE, subType = LogRecordConstants.TEACHER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}", success = LogRecordConstants.TEACHER_UPDATE_SUCCESS)
    public void updateTeacher(TeacherUpdateReqVO reqVO) {
        // 获取更新前的讲师信息 - 用于操作日志的字段对比
        TeacherDO oldTeacher = teacherMapper.selectById(reqVO.getId());
        if (oldTeacher == null) {
            throw new IllegalArgumentException("讲师不存在");
        }
        
        // 1. 更新主表
        TeacherDO teacher = teacherConvert.convert(reqVO);
        teacher.setUpdateTime(new Date());
        teacher.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        teacherMapper.updateById(teacher);
        
        // 2. 智能更新资质文件（有ID的更新，没有ID的新增，删除不存在的）
        String certChangeLog = updateTeacherCerts(teacher.getId(), reqVO.getCertFiles());
        
        // 手动比较字段变更，避免_DIFF函数的误报问题
        StringBuilder fieldChanges = new StringBuilder();
        
        // 比较基本字段
        if (!Objects.equals(oldTeacher.getName(), reqVO.getName())) {
            fieldChanges.append("【讲师姓名】从【").append(oldTeacher.getName()).append("】修改为【").append(reqVO.getName()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getDescription(), reqVO.getDescription())) {
            fieldChanges.append("【简介】从【").append(oldTeacher.getDescription()).append("】修改为【").append(reqVO.getDescription()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getType(), reqVO.getType())) {
            fieldChanges.append("【讲师类型】从【").append(oldTeacher.getType()).append("】修改为【").append(reqVO.getType()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getBiz(), reqVO.getBiz())) {
            fieldChanges.append("【业务模块】从【").append(oldTeacher.getBiz()).append("】修改为【").append(reqVO.getBiz()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getOrg(), reqVO.getOrg())) {
            fieldChanges.append("【关联机构】从【").append(oldTeacher.getOrg()).append("】修改为【").append(reqVO.getOrg()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getField(), reqVO.getField())) {
            fieldChanges.append("【擅长领域】从【").append(oldTeacher.getField()).append("】修改为【").append(reqVO.getField()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getPhone(), reqVO.getPhone())) {
            fieldChanges.append("【联系电话】从【").append(oldTeacher.getPhone()).append("】修改为【").append(reqVO.getPhone()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getEmail(), reqVO.getEmail())) {
            fieldChanges.append("【邮箱地址】从【").append(oldTeacher.getEmail()).append("】修改为【").append(reqVO.getEmail()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getStatus(), reqVO.getStatus())) {
            fieldChanges.append("【合作状态】从【").append(oldTeacher.getStatus()).append("】修改为【").append(reqVO.getStatus()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getSignStatus(), reqVO.getSignStatus())) {
            fieldChanges.append("【电子签约状态】从【").append(oldTeacher.getSignStatus()).append("】修改为【").append(reqVO.getSignStatus()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractType(), reqVO.getContractType())) {
            fieldChanges.append("【合同类型】从【").append(oldTeacher.getContractType()).append("】修改为【").append(reqVO.getContractType()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractTemplate(), reqVO.getContractTemplate())) {
            fieldChanges.append("【合同模板】从【").append(oldTeacher.getContractTemplate()).append("】修改为【").append(reqVO.getContractTemplate()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractNo(), reqVO.getContractNo())) {
            fieldChanges.append("【合同编号】从【").append(oldTeacher.getContractNo()).append("】修改为【").append(reqVO.getContractNo()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractName(), reqVO.getContractName())) {
            fieldChanges.append("【合同名称】从【").append(oldTeacher.getContractName()).append("】修改为【").append(reqVO.getContractName()).append("】；");
        }
        if (!isBigDecimalEqual(oldTeacher.getContractAmount(), reqVO.getContractAmount())) {
            fieldChanges.append("【合同金额】从【").append(oldTeacher.getContractAmount()).append("】修改为【").append(reqVO.getContractAmount()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractFileName(), reqVO.getContractFileName())) {
            fieldChanges.append("【纸质合同附件文件名】从【").append(oldTeacher.getContractFileName()).append("】修改为【").append(reqVO.getContractFileName()).append("】；");
        }
        if (!Objects.equals(oldTeacher.getContractFileUrl(), reqVO.getContractFileUrl())) {
            fieldChanges.append("【纸质合同附件文件URL】从【").append(oldTeacher.getContractFileUrl()).append("】修改为【").append(reqVO.getContractFileUrl()).append("】；");
        }
        
        // 比较日期字段，只比较日期部分，忽略时间部分
        if (!isDateEqual(oldTeacher.getSignDate(), reqVO.getSignDate())) {
            fieldChanges.append("【签约日期】从【").append(formatDate(oldTeacher.getSignDate())).append("】修改为【").append(formatDate(reqVO.getSignDate())).append("】；");
        }
        if (!isDateEqual(oldTeacher.getContractPeriodStart(), reqVO.getContractPeriodStart())) {
            fieldChanges.append("【合同周期开始】从【").append(formatDate(oldTeacher.getContractPeriodStart())).append("】修改为【").append(formatDate(reqVO.getContractPeriodStart())).append("】；");
        }
        if (!isDateEqual(oldTeacher.getContractPeriodEnd(), reqVO.getContractPeriodEnd())) {
            fieldChanges.append("【合同周期结束】从【").append(formatDate(oldTeacher.getContractPeriodEnd())).append("】修改为【").append(formatDate(reqVO.getContractPeriodEnd())).append("】；");
        }
        
        // 记录操作日志上下文
        LogRecordContext.putVariable("teacher", oldTeacher);
        LogRecordContext.putVariable("updateReqVO", reqVO);
        LogRecordContext.putVariable("fieldChanges", fieldChanges.toString());
        
        // 如果有资质文件变更，添加到日志上下文中
        if (certChangeLog != null && !certChangeLog.isEmpty()) {
            LogRecordContext.putVariable("certChangeLog", certChangeLog);
        }
    }

    /**
     * 删除讲师（逻辑删除）
     * 
     * 操作日志记录：
     * - 记录删除操作，包含讲师姓名
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param id 讲师ID
     */
    @Override
    @Transactional
    @LogRecord(type = LogRecordConstants.TEACHER_TYPE, subType = LogRecordConstants.TEACHER_DELETE_SUB_TYPE, bizNo = "{{#teacher.id}}", success = LogRecordConstants.TEACHER_DELETE_SUCCESS)
    public void deleteTeacher(Long id) {
        // 参数校验
        if (id == null) {
            throw new IllegalArgumentException("删除参数不能为空");
        }

        TeacherDO teacher = teacherMapper.selectById(id);
        if (teacher == null) {
            throw new IllegalArgumentException("讲师不存在");
        }

        if (!Boolean.TRUE.equals(teacher.getDeleted())) {
            teacher.setDeleted(true);
            teacher.setUpdateTime(new Date());
            teacher.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            teacherMapper.updateById(teacher);
            
            // 记录操作日志上下文 - 用于日志模板中的变量替换
            LogRecordContext.putVariable("teacher", teacher);
        }
        
        // 2. 逻辑删除所有资质
        List<TeacherCertDO> certs = teacherCertMapper.selectList(
            new LambdaQueryWrapperX<TeacherCertDO>()
                .eq(TeacherCertDO::getTeacherId, id)
                .eq(TeacherCertDO::getDeleted, false));
        
        // 记录删除的资质文件
        if (CollUtil.isNotEmpty(certs)) {
            StringBuilder certDeleteLog = new StringBuilder("同时删除了资质文件：");
            for (TeacherCertDO cert : certs) {
                certDeleteLog.append("【").append(cert.getCertName()).append("】");
            }
            LogRecordContext.putVariable("certDeleteLog", certDeleteLog.toString());
        }
        
        for (TeacherCertDO cert : certs) {
            cert.setDeleted(true);
            teacherCertMapper.updateById(cert);
        }
    }

    @Override
    public TeacherRespVO getTeacherDetail(Long id) {
        TeacherDO teacher = teacherMapper.selectById(id);
        if (teacher == null || Boolean.TRUE.equals(teacher.getDeleted())) return null;
        TeacherRespVO vo = teacherConvert.convert(teacher);
        // 查询资质
        List<TeacherCertDO> certs = teacherCertMapper.selectList(
            new LambdaQueryWrapperX<TeacherCertDO>()
                .eq(TeacherCertDO::getTeacherId, id)
                .eq(TeacherCertDO::getDeleted, false));
        vo.setCertFiles(teacherCertConvert.convertList(certs));
        return vo;
    }

    @Override
    public PageResult<TeacherRespVO> getTeacherPage(TeacherPageReqVO reqVO) {
        // 构建分页条件
        LambdaQueryWrapperX<TeacherDO> qw = new LambdaQueryWrapperX<>();
        qw.eqIfPresent(TeacherDO::getType, reqVO.getType())
           .eqIfPresent(TeacherDO::getBiz, reqVO.getBiz())
           .eqIfPresent(TeacherDO::getStatus, reqVO.getStatus())
           .eq(TeacherDO::getDeleted, false);
        
        // 关键字搜索：同时匹配讲师姓名和关联机构
        if (reqVO.getKeyword() != null && !reqVO.getKeyword().trim().isEmpty()) {
            qw.and(w -> w.like(TeacherDO::getName, reqVO.getKeyword().trim())
                        .or()
                        .like(TeacherDO::getOrg, reqVO.getKeyword().trim()));
        }
        
        qw.orderByDesc(TeacherDO::getId);
        
        // 分页查询
        List<TeacherDO> list = teacherMapper.selectList(qw);
        long total = teacherMapper.selectCount(qw);
        List<TeacherRespVO> voList = teacherConvert.convertList(list);
        
        // 组装每个讲师的资质
        for (TeacherRespVO vo : voList) {
            List<TeacherCertDO> certs = teacherCertMapper.selectList(
                new LambdaQueryWrapperX<TeacherCertDO>()
                    .eq(TeacherCertDO::getTeacherId, vo.getId())
                    .eq(TeacherCertDO::getDeleted, false));
            vo.setCertFiles(teacherCertConvert.convertList(certs));
        }
        return new PageResult<>(voList, total);
    }

    @Override
    public List<TeacherListRespVO> getTeacherList() {
        // 构建查询条件
        LambdaQueryWrapperX<TeacherDO> qw = new LambdaQueryWrapperX<>();
        qw.eq(TeacherDO::getDeleted, false)
          .orderByDesc(TeacherDO::getId);

        // 查询所有讲师
        List<TeacherDO> list = teacherMapper.selectList(qw);

        // 转换为列表VO
        return teacherConvert.convertToListVOList(list);
    }

    @Override
    public TeacherStatRespVO getTeacherStat() {
        // 统计总数、内部讲师、外部讲师、待合作
        LambdaQueryWrapperX<TeacherDO> qw = new LambdaQueryWrapperX<>();
        qw.eq(TeacherDO::getDeleted, false);
        int total = teacherMapper.selectCount(qw).intValue();
        int inner = teacherMapper.selectCount(qw.clone().eq(TeacherDO::getType, "内部讲师")).intValue();
        int outer = teacherMapper.selectCount(qw.clone().eq(TeacherDO::getType, "外部讲师")).intValue();
        int pending = teacherMapper.selectCount(qw.clone().eq(TeacherDO::getSignStatus, "未签约")).intValue();
        TeacherStatRespVO stat = new TeacherStatRespVO();
        stat.setTotal(total);
        stat.setInner(inner);
        stat.setOuter(outer);
        stat.setPending(pending);
        return stat;
    }

    /**
     * 智能更新讲师资质文件
     * 逻辑：有ID的更新，没有ID的新增，删除不存在的
     * 
     * @param teacherId 讲师ID
     * @param certFiles 新的资质文件列表
     * @return 变更日志信息
     */
    private String updateTeacherCerts(Long teacherId, List<TeacherCertSaveReqVO> certFiles) {
        // 获取现有的资质文件
        List<TeacherCertDO> existingCerts = teacherCertMapper.selectList(
            new LambdaQueryWrapperX<TeacherCertDO>()
                .eq(TeacherCertDO::getTeacherId, teacherId)
                .eq(TeacherCertDO::getDeleted, false));
        
        // 构建变更日志
        StringBuilder changeLog = new StringBuilder();
        List<String> deletedCerts = new ArrayList<>();
        List<String> updatedCerts = new ArrayList<>();
        List<String> addedCerts = new ArrayList<>();
        
        // 处理新的资质文件列表
        if (CollUtil.isNotEmpty(certFiles)) {
            for (TeacherCertSaveReqVO certVO : certFiles) {
                if (certVO.getId() != null) {
                    // 有ID，进行更新
                    TeacherCertDO existingCert = existingCerts.stream()
                        .filter(cert -> cert.getId().equals(certVO.getId()))
                        .findFirst()
                        .orElse(null);
                    
                    if (existingCert != null) {
                        // 检查内容是否真正发生变化
                        boolean hasChanged = !isCertContentSame(existingCert, certVO);
                        
                        if (hasChanged) {
                            // 内容有变化，执行更新操作
                            TeacherCertDO cert = teacherCertConvert.convert(certVO);
                            cert.setId(certVO.getId());
                            cert.setTeacherId(teacherId);
                            // 保留原有的创建时间和创建者
                            cert.setCreateTime(existingCert.getCreateTime());
                            cert.setCreator(existingCert.getCreator());
                            cert.setUpdateTime(new Date());
                            cert.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
                            cert.setDeleted(false); // 确保不被删除
                            teacherCertMapper.updateById(cert);
                            updatedCerts.add(certVO.getCertName());
                        }
                        // 无论是否更新，都从现有列表中移除，避免被删除
                        existingCerts.removeIf(c -> c.getId().equals(certVO.getId()));
                    } else {
                        // 有ID但找不到现有记录，可能是数据不一致，记录警告并作为新增处理
                        TeacherCertDO cert = teacherCertConvert.convert(certVO);
                        cert.setTeacherId(teacherId);
                        cert.setCreateTime(new Date());
                        cert.setDeleted(false);
                        cert.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                        teacherCertMapper.insert(cert);
                        addedCerts.add(certVO.getCertName() + "(原ID:" + certVO.getId() + ")");
                    }
                } else {
                    // 没有ID，进行新增
                    TeacherCertDO cert = teacherCertConvert.convert(certVO);
                    cert.setTeacherId(teacherId);
                    cert.setCreateTime(new Date());
                    cert.setDeleted(false);
                    cert.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                    teacherCertMapper.insert(cert);
                    addedCerts.add(certVO.getCertName());
                }
            }
        }
        
        // 删除剩余的现有资质文件（不在新列表中的）
        for (TeacherCertDO cert : existingCerts) {
            cert.setDeleted(true);
            cert.setUpdateTime(new Date());
            cert.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            teacherCertMapper.updateById(cert);
            deletedCerts.add(cert.getCertName());
        }
        
        // 构建变更日志
        if (!deletedCerts.isEmpty() || !updatedCerts.isEmpty() || !addedCerts.isEmpty()) {
            changeLog.append("资质文件变更：");
            
            if (!deletedCerts.isEmpty()) {
                changeLog.append("删除了");
                for (String certName : deletedCerts) {
                    changeLog.append("【").append(certName).append("】");
                }
                changeLog.append("；");
            }
            
            if (!updatedCerts.isEmpty()) {
                changeLog.append("更新了");
                for (String certName : updatedCerts) {
                    changeLog.append("【").append(certName).append("】");
                }
                changeLog.append("；");
            }
            
            if (!addedCerts.isEmpty()) {
                changeLog.append("新增了");
                for (String certName : addedCerts) {
                    changeLog.append("【").append(certName).append("】");
                }
            }
        }
        
        return changeLog.toString();
    }

    /**
     * 比较两个资质文件的内容是否相同
     * 
     * @param existingCert 现有的资质文件DO
     * @param certVO 新的资质文件VO
     * @return 如果内容相同返回true，否则返回false
     */
    private boolean isCertContentSame(TeacherCertDO existingCert, TeacherCertSaveReqVO certVO) {
        // 比较关键字段
        return Objects.equals(existingCert.getCertType(), certVO.getCertType()) &&
               Objects.equals(existingCert.getCertName(), certVO.getCertName()) &&
               Objects.equals(existingCert.getFileName(), certVO.getFileName()) &&
               Objects.equals(existingCert.getFileUrl(), certVO.getFileUrl()) &&
               isDateEqual(existingCert.getValidStartDate(), certVO.getValidStartDate()) &&
               isDateEqual(existingCert.getValidEndDate(), certVO.getValidEndDate());
    }

    /**
     * 校验导入讲师的必填项和业务规则，返回错误信息（无错返回null）
     */
    private String checkImportTeacher(TeacherSaveReqVO reqVO, int rowNum) {
        String rowPrefix = "第" + rowNum + "行：";
        if (reqVO.getName() == null || reqVO.getName().trim().isEmpty()) {
            return rowPrefix + "讲师姓名不能为空";
        }
        if (reqVO.getType() == null || reqVO.getType().trim().isEmpty()) {
            return rowPrefix + "讲师类型不能为空";
        }
        if (!reqVO.getType().equals("内部讲师") && !reqVO.getType().equals("外部讲师")) {
            return rowPrefix + "讲师类型只能为‘内部讲师’或‘外部讲师’";
        }
        if (reqVO.getBiz() == null || reqVO.getBiz().trim().isEmpty()) {
            return rowPrefix + "业务模块不能为空";
        }
        if (!Arrays.asList("高校业务", "家政业务", "培训业务", "认证业务").contains(reqVO.getBiz())) {
            return rowPrefix + "业务模块只能为‘高校业务/家政业务/培训业务/认证业务’";
        }
        if (reqVO.getField() == null || reqVO.getField().trim().isEmpty()) {
            return rowPrefix + "擅长领域不能为空";
        }
        if (reqVO.getStatus() == null || reqVO.getStatus().trim().isEmpty()) {
            return rowPrefix + "合作状态不能为空";
        }
        if (!reqVO.getStatus().equals("合作中") && !reqVO.getStatus().equals("待签约")) {
            return rowPrefix + "合作状态只能为‘合作中’或‘待签约’";
        }
        return null;
    }

    @Override
    @Transactional
    public TeacherImportRespVO importValidTeachers(List<TeacherSaveReqVO> teacherList) {
        TeacherImportRespVO respVO = new TeacherImportRespVO();
        List<String> failReasons = new ArrayList<>();
        
        int totalCount = teacherList.size();
        int successCount = 0;
        int failCount = 0;
        
        for (int i = 0; i < teacherList.size(); i++) {
            TeacherSaveReqVO reqVO = teacherList.get(i);
            
            try {
                // 再次校验（双重保险）
                String errorMessage = checkImportTeacher(reqVO, i + 1);
                if (errorMessage != null) {
                    failReasons.add("第" + (i + 1) + "行：" + errorMessage);
                    failCount++;
                    continue;
                }
                
                // 创建讲师
                TeacherDO teacher = teacherConvert.convert(reqVO);
                teacher.setCreateTime(new Date());
                teacher.setDeleted(false);
                teacher.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                teacher.setTenantId(
                    SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
                teacherMapper.insert(teacher);
                
                // 创建资质文件
                if (CollUtil.isNotEmpty(reqVO.getCertFiles())) {
                    for (TeacherCertSaveReqVO certVO : reqVO.getCertFiles()) {
                        TeacherCertDO cert = teacherCertConvert.convert(certVO);
                        cert.setTeacherId(teacher.getId());
                        cert.setCreateTime(new Date());
                        cert.setDeleted(false);
                        cert.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                        teacherCertMapper.insert(cert);
                    }
                }
                
                successCount++;
                
            } catch (Exception e) {
                failReasons.add("第" + (i + 1) + "行：导入失败 - " + e.getMessage());
                failCount++;
            }
        }
        
        respVO.setTotalCount(totalCount);
        respVO.setSuccessCount(successCount);
        respVO.setFailCount(failCount);
        respVO.setFailReasons(String.join("; ", failReasons));
        
        return respVO;
    }

    @Override
    public TeacherImportValidateRespVO validateImportExcelTeachers(List<TeacherImportExcelVO> excelList) {
        TeacherImportValidateRespVO respVO = new TeacherImportValidateRespVO();
        List<TeacherImportValidateItemVO> validateList = new ArrayList<>();
        
        int totalCount = excelList.size();
        int validCount = 0;
        int errorCount = 0;
        
        for (int i = 0; i < excelList.size(); i++) {
            TeacherImportExcelVO excelVO = excelList.get(i);
            TeacherImportValidateItemVO itemVO = new TeacherImportValidateItemVO();
            
            // 设置基本信息
            itemVO.setRowNum(i + 1);
            itemVO.setName(excelVO.getName());
            itemVO.setType(excelVO.getType());
            itemVO.setBiz(excelVO.getBiz());
            itemVO.setOrg(excelVO.getOrg());
            itemVO.setField(excelVO.getField());
            itemVO.setPhone(excelVO.getPhone());
            itemVO.setEmail(excelVO.getEmail());
            itemVO.setDescription(excelVO.getDescription());
            itemVO.setStatus(excelVO.getStatus());
            
            // 转换为TeacherSaveReqVO进行校验
            TeacherSaveReqVO reqVO = new TeacherSaveReqVO();
            reqVO.setName(excelVO.getName());
            reqVO.setType(excelVO.getType());
            reqVO.setBiz(excelVO.getBiz());
            reqVO.setOrg(excelVO.getOrg());
            reqVO.setField(excelVO.getField());
            reqVO.setPhone(excelVO.getPhone());
            reqVO.setEmail(excelVO.getEmail());
            reqVO.setDescription(excelVO.getDescription());
            reqVO.setStatus(excelVO.getStatus());
            itemVO.setOriginalData(reqVO);
            
            // 校验数据
            String errorMessage = checkImportTeacher(reqVO, i + 1);
            if (errorMessage != null) {
                itemVO.setValidateStatus("ERROR");
                itemVO.setErrorMessage(errorMessage);
                errorCount++;
            } else {
                itemVO.setValidateStatus("VALID");
                itemVO.setErrorMessage("");
                validCount++;
            }
            
            validateList.add(itemVO);
        }
        
        respVO.setValidateList(validateList);
        respVO.setTotalCount(totalCount);
        respVO.setValidCount(validCount);
        respVO.setErrorCount(errorCount);
        
        return respVO;
    }

    /**
     * 比较两个日期是否相等（只比较日期部分，忽略时间部分）
     * 
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果日期部分相同返回true，否则返回false
     */
    private boolean isDateEqual(Date date1, Date date2) {
        if (date1 == null && date2 == null) {
            return true;
        }
        if (date1 == null || date2 == null) {
            return false;
        }
        
        // 使用DateUtil只比较日期部分，忽略时间部分
        return cn.hutool.core.date.DateUtil.isSameDay(date1, date2);
    }

    /**
     * 格式化日期为 yyyy-MM-dd 格式
     * 
     * @param date 日期
     * @return 格式化后的日期字符串，如果日期为null则返回空字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        return cn.hutool.core.date.DateUtil.format(date, "yyyy-MM-dd");
    }

    /**
     * 比较两个BigDecimal是否相等（比较数值，忽略精度）
     * 
     * @param bd1 第一个BigDecimal
     * @param bd2 第二个BigDecimal
     * @return 如果数值相等返回true，否则返回false
     */
    private boolean isBigDecimalEqual(BigDecimal bd1, BigDecimal bd2) {
        if (bd1 == null && bd2 == null) {
            return true;
        }
        if (bd1 == null || bd2 == null) {
            return false;
        }
        
        // 使用compareTo方法比较数值，忽略精度
        return bd1.compareTo(bd2) == 0;
    }
} 