package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;

import javax.validation.Valid;
import java.util.List;

public interface PractitionerService {
    Long createPractitioner(@Valid PractitionerSaveReqVO createReqVO);

    void updatePractitioner(@Valid PractitionerUpdateReqVO updateReqVO);

    void deletePractitioner(Long id);

    PractitionerRespVO getPractitioner(Long id);

    PageResult<PractitionerRespVO> getPractitionerPage(PractitionerPageReqVO pageReqVO);

    void updatePractitionerStatus(PractitionerStatusUpdateReqVO reqVO);

    void updatePractitionerRating(PractitionerRatingUpdateReqVO reqVO);

    List<PractitionerDO> getPractitionerList(PractitionerPageReqVO pageReqVO);


} 