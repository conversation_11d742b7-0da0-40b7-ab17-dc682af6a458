package cn.bztmaster.cnt.module.publicbiz.service.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteManagementDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 场地管理 Service 接口
 *
 * <AUTHOR>
 */
public interface SiteManagementService {

    /**
     * 创建场地
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSiteManagement(@Valid SiteManagementSaveReqVO createReqVO);

    /**
     * 更新场地
     *
     * @param updateReqVO 更新信息
     */
    void updateSiteManagement(@Valid SiteManagementSaveReqVO updateReqVO);

    /**
     * 删除场地
     *
     * @param id 编号
     */
    void deleteSiteManagement(Long id);

    /**
     * 获得场地
     *
     * @param id 编号
     * @return 场地
     */
    SiteManagementDO getSiteManagement(Long id);

    /**
     * 获得场地分页
     *
     * @param pageReqVO 分页查询
     * @return 场地分页
     */
    PageResult<SiteManagementDO> getSiteManagementPage(SiteManagementPageReqVO pageReqVO);

    /**
     * 获得场地列表
     *
     * @param listReqVO 查询条件
     * @return 场地列表
     */
    List<SiteManagementDO> getSiteManagementList(SiteManagementListReqVO listReqVO);

    /**
     * 获得场地统计
     *
     * @param campus 校区筛选（可选）
     * @param campusName 校区名称筛选（可选）
     * @return 统计信息
     */
    SiteManagementStatisticsRespVO getSiteManagementStatistics(String campus, String campusName);

    /**
     * 校验场地存在
     *
     * @param id 编号
     * @return 场地信息
     */
    SiteManagementDO validateSiteManagementExists(Long id);

    /**
     * 校验场地名称唯一性
     *
     * @param id 编号（更新时传入，新增时为null）
     * @param name 场地名称
     */
    void validateSiteManagementNameUnique(Long id, String name);

    /**
     * 校验场地是否可预约
     *
     * @param id 编号
     * @return 场地信息
     */
    SiteManagementDO validateSiteManagementBookable(Long id);

}
