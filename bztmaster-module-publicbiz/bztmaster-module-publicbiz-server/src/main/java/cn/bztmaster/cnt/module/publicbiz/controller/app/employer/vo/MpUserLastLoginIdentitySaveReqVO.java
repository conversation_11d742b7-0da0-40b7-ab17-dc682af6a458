package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 小程序用户最后登录身份记录 Save Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "小程序用户最后登录身份记录 Save Request VO")
@Data
public class MpUserLastLoginIdentitySaveReqVO {

    @Schema(description = "主键ID", example = "1024")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "微信openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    @NotNull(message = "微信openid不能为空")
    private String openid;

    @Schema(description = "微信unionid", example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String unionid;

    @Schema(description = "身份类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    @NotNull(message = "身份类型不能为空")
    private String identityType;

    @Schema(description = "身份关联ID", example = "1024")
    private Long identityId;

    @Schema(description = "身份名称", example = "张先生")
    private String identityName;

    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最后登录时间不能为空")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    @Schema(description = "设备信息", example = "iPhone 14 Pro")
    private String deviceInfo;

    @Schema(description = "登录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "登录状态不能为空")
    private Integer loginStatus;

    @Schema(description = "微信session_key", example = "session_key_example")
    private String sessionKey;

    @Schema(description = "访问令牌", example = "access_token_example")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "refresh_token_example")
    private String refreshToken;

    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpireTime;

}