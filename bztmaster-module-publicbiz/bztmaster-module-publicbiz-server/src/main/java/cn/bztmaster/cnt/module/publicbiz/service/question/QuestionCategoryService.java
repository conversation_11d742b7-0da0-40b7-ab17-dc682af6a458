package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;

import java.util.Collection;
import java.util.List;

/**
 * 考题分类服务接口
 *
 * <AUTHOR>
 */
public interface QuestionCategoryService {

    /**
     * 分类分页查询
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<QuestionCategoryRespVO> pageQuestionCategory(QuestionCategoryPageReqVO reqVO);

    /**
     * 分类列表查询（不分页）
     *
     * @param biz 业务模块
     * @param levels 分类层级列表
     * @param parentId 父级分类ID
     * @return 分类列表
     */
    List<QuestionCategoryRespVO> listQuestionCategory(String biz, List<Integer> levels, Long parentId);

    /**
     * 根据ID查询分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    QuestionCategoryRespVO getQuestionCategory(Long id);

    /**
     * 新增分类
     *
     * @param reqVO 分类信息
     * @return 分类ID
     */
    Long createQuestionCategory(QuestionCategorySaveReqVO reqVO);

    /**
     * 编辑分类
     *
     * @param reqVO 分类信息
     */
    void updateQuestionCategory(QuestionCategorySaveReqVO reqVO);

    /**
     * 删除分类
     *
     * @param id 分类ID
     */
    void deleteQuestionCategory(Long id);

    // ==================== API接口需要的方法 ====================

    /**
     * 根据ID查询分类DO
     *
     * @param id 分类ID
     * @return 分类DO
     */
    QuestionCategoryDO getQuestionCategoryDO(Long id);

    /**
     * 根据ID列表查询分类DO列表
     *
     * @param ids 分类ID列表
     * @return 分类DO列表
     */
    List<QuestionCategoryDO> getQuestionCategoryDOList(Collection<Long> ids);

    /**
     * 校验分类是否存在
     *
     * @param id 分类ID
     */
    void validateQuestionCategoryExists(Long id);

}
