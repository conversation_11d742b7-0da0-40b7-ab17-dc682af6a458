package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntPunchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨打卡")
@RestController
@RequestMapping("/publicbiz/aunt/punch")
@Validated
public class AuntPunchController {

    @Resource
    private AuntPunchService auntPunchService;

    @PostMapping("/record")
    @Operation(summary = "记录打卡")
    @PermitAll
    public CommonResult<Boolean> recordPunch(@Valid @RequestBody AuntPunchRecordReqVO reqVO) {
        // 获取当前登录阿姨OneID
        String auntOneId = SecurityFrameworkUtils.getLoginUserId().toString();
        
        // 设置阿姨OneID
        reqVO.setAuntOneId(auntOneId);
        
        // 记录打卡
        auntPunchService.recordPunch(reqVO);
        
        return success(Boolean.TRUE);
    }

    @GetMapping("/record/list")
    @Operation(summary = "获得打卡记录列表")
    @PermitAll
    public CommonResult<AuntPunchRecordRespVO> getPunchRecordList(
            @RequestParam(required = false) String scheduleId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        // 获取当前登录阿姨OneID
        String auntOneId = SecurityFrameworkUtils.getLoginUserId().toString();
        
        // 获取打卡记录列表
        AuntPunchRecordRespVO result = auntPunchService.getPunchRecordList(auntOneId, scheduleId, startDate, endDate);
        
        return success(result);
    }

} 