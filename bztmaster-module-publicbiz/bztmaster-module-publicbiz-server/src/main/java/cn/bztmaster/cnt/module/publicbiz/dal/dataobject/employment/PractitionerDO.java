package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_practitioner")
@Schema(description = "阿姨基本信息表 DO")
public class PractitionerDO {
    @TableId
    private Long id;
    private String auntOneid;
    private String name;
    private String phone;
    private String idCard;
    private String hometown;
    private Integer age;
    private String gender;
    private String avatar;
    private String serviceType;
    private Integer experienceYears;
    private String platformStatus;
    private BigDecimal rating;
    private Long agencyId;
    private String agencyName;
    private String status;
    private String currentStatus;
    private String currentOrderId;
    private Integer totalOrders;
    private BigDecimal totalIncome;
    private BigDecimal customerSatisfaction;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 