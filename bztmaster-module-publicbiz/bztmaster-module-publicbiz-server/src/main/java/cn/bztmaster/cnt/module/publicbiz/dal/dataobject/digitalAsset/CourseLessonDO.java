package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程课时表 DO（线上课程专用）
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_course_lesson")
@Schema(description = "课程课时表 DO")
public class CourseLessonDO extends BaseDO {

    /**
     * 课时ID
     */
    @TableId
    @Schema(description = "课时ID", example = "1")
    private Long id;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    /**
     * 章节ID
     */
    @Schema(description = "章节ID", example = "1001")
    private Long chapterId;

    /**
     * 课时标题
     */
    @Schema(description = "课时标题", example = "课时1：SWOT分析法概述")
    private String title;

    /**
     * 课时类型：视频、文档、音频
     */
    @Schema(description = "课时类型", example = "视频")
    private String lessonType;

    /**
     * 是否免费试看
     */
    @Schema(description = "是否免费试看", example = "true")
    private Boolean isFree;

    /**
     * 关联素材ID
     */
    @Schema(description = "关联素材ID", example = "material001")
    private String materialId;

    /**
     * 关联素材名称
     */
    @Schema(description = "关联素材名称", example = "课程导论.mp4")
    private String materialName;

    /**
     * 关联素材文件URL
     */
    @Schema(description = "关联素材文件URL", example = "https://example.com/video1.mp4")
    private String materialFileUrl;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;
}
