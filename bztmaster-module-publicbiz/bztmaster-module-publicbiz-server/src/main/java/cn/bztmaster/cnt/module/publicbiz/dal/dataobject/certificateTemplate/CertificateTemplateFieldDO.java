package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 证书模板字段配置 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_certificate_template_field")
@KeySequence("publicbiz_certificate_template_field_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class CertificateTemplateFieldDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    /**
     * 证书模板ID，关联publicbiz_certificate_template.id
     */
    private Long templateId;

    /**
     * 字段唯一标识，如field_1
     */
    private String fieldId;

    /**
     * 字段类型：name-学员姓名，code-证书编号，id-身份证，date-发证日期，qrcode-证书查验二维码
     */
    private String fieldType;

    /**
     * 字段显示标签，如【学员姓名】
     */
    private String fieldLabel;

    /**
     * 字段X坐标位置（像素）
     */
    private Integer positionX;

    /**
     * 字段Y坐标位置（像素）
     */
    private Integer positionY;

    /**
     * 字体大小（像素）
     */
    private Integer fontSize;

    /**
     * 字体颜色，十六进制色值
     */
    private String fontColor;

    /**
     * 字体族：微软雅黑，黑体，Arial，Times New Roman，宋体
     */
    private String fontFamily;

    /**
     * 是否选中状态，0-未选中，1-选中
     */
    private Boolean isSelected;

    /**
     * 字段排序顺序
     */
    private Integer sortOrder;

    /**
     * 创建人姓名
     */
    private String creatorName;

}