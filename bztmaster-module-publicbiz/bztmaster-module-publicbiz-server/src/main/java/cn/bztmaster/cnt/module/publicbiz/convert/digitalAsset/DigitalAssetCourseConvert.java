package cn.bztmaster.cnt.module.publicbiz.convert.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset.DigitalAssetCourseDO;
import cn.bztmaster.cnt.module.publicbiz.api.digitalAsset.dto.DigitalAssetCourseRespDTO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 数字资产课程 Convert
 * 
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface DigitalAssetCourseConvert {
    
    DigitalAssetCourseConvert INSTANCE = Mappers.getMapper(DigitalAssetCourseConvert.class);

    /**
     * VO 转 DO
     */
    DigitalAssetCourseDO convert(DigitalAssetCourseSaveReqVO bean);

    /**
     * DO 转 VO
     */
    DigitalAssetCourseRespVO convert(DigitalAssetCourseDO bean);

    /**
     * DO 列表转 VO 列表
     */
    List<DigitalAssetCourseRespVO> convertList(List<DigitalAssetCourseDO> list);

    /**
     * DO 分页转 VO 分页
     */
    PageResult<DigitalAssetCourseRespVO> convertPage(PageResult<DigitalAssetCourseDO> page);

    /**
     * DO 转 DTO（用于API接口）
     */
    DigitalAssetCourseRespDTO convertToDTO(DigitalAssetCourseDO bean);

    /**
     * DO 列表转 DTO 列表（用于API接口）
     */
    List<DigitalAssetCourseRespDTO> convertToDTOList(List<DigitalAssetCourseDO> list);
}
