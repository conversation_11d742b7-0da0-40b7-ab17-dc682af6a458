package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务模块枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionBizEnum {

    HOUSEKEEPING("家政业务", "家政服务相关业务"),
    UNIVERSITY("高校业务", "高等院校相关业务"),
    TRAINING("培训业务", "职业培训相关业务"),
    CERTIFICATION("认证业务", "职业认证相关业务");

    /**
     * 业务模块值
     */
    private final String value;

    /**
     * 业务模块说明
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static QuestionBizEnum getByValue(String value) {
        for (QuestionBizEnum bizEnum : values()) {
            if (bizEnum.getValue().equals(value)) {
                return bizEnum;
            }
        }
        return null;
    }

    /**
     * 校验业务模块是否有效
     *
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(String value) {
        return getByValue(value) != null;
    }

}
