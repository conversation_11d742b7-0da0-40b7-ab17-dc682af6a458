package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 难度等级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionDifficultyEnum {

    EASY(1, "简单"),
    MEDIUM(2, "中等"),
    HARD(3, "困难");

    /**
     * 难度等级值
     */
    private final Integer value;

    /**
     * 难度等级说明
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static QuestionDifficultyEnum getByValue(Integer value) {
        for (QuestionDifficultyEnum difficultyEnum : values()) {
            if (difficultyEnum.getValue().equals(value)) {
                return difficultyEnum;
            }
        }
        return null;
    }

    /**
     * 校验难度等级是否有效
     *
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }

}
