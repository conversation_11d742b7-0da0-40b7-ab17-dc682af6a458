package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;

/**
 * 课程课时 Service 接口
 * 
 * <AUTHOR>
 */
public interface CourseLessonService {

    /**
     * 创建课时
     * 
     * @param reqVO 创建信息
     * @return 课时ID
     */
    Long createCourseLesson(CourseLessonSaveReqVO reqVO);

    /**
     * 更新课时
     * 
     * @param reqVO 更新信息
     */
    void updateCourseLesson(CourseLessonSaveReqVO reqVO);

    /**
     * 删除课时
     * 
     * @param id 课时ID
     */
    void deleteCourseLesson(Long id);
}
