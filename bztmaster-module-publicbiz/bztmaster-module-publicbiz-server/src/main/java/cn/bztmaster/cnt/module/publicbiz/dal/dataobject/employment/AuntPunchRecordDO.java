package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_aunt_punch_record")
@Schema(description = "阿姨打卡记录表 DO")
public class AuntPunchRecordDO {
    @TableId
    private Long id;
    private String scheduleId;
    private String auntOneid;
    private String auntName;
    private Integer punchType; // 1-开始打卡,2-完成打卡
    private Date punchTime;
    private String punchLocation;
    private BigDecimal punchLatitude;
    private BigDecimal punchLongitude;
    private Integer photoCount;
    private String photoUrls;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
} 