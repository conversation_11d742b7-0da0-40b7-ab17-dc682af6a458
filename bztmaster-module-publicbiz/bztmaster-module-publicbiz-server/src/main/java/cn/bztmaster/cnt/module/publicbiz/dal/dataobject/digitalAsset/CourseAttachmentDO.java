package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程附件表 DO
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_course_attachment")
@Schema(description = "课程附件表 DO")
public class CourseAttachmentDO extends BaseDO {

    /**
     * 附件ID
     */
    @TableId
    @Schema(description = "附件ID", example = "1")
    private Long id;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称", example = "开课须知.md")
    private String attachmentName;

    /**
     * 附件类型：视频、文档、音频
     */
    @Schema(description = "附件类型", example = "文档")
    private String attachmentType;

    /**
     * 文件URL
     */
    @Schema(description = "文件URL", example = "https://example.com/notice.md")
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小", example = "2048")
    private Long fileSize;
}
