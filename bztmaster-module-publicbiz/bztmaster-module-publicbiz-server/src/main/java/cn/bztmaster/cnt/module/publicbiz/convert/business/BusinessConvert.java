package cn.bztmaster.cnt.module.publicbiz.convert.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessDO;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface BusinessConvert {
    BusinessConvert INSTANCE = Mappers.getMapper(BusinessConvert.class);

    BusinessDO convert(BusinessSaveReqVO bean);
    BusinessRespVO convert(BusinessDO bean);
    List<BusinessRespVO> convertList(List<BusinessDO> list);
    PageResult<BusinessRespVO> convertPage(PageResult<BusinessDO> page);
} 