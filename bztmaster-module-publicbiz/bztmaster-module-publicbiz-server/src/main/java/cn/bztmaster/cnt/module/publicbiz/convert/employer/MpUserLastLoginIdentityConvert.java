package cn.bztmaster.cnt.module.publicbiz.convert.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentitySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO;
import cn.bztmaster.cnt.module.publicbiz.enums.IdentityTypeEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.LoginStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 小程序用户最后登录身份记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MpUserLastLoginIdentityConvert {

    MpUserLastLoginIdentityConvert INSTANCE = Mappers.getMapper(MpUserLastLoginIdentityConvert.class);

    @Mapping(target = "identityTypeDesc", source = "identityType", qualifiedByName = "convertIdentityTypeDesc")
    @Mapping(target = "loginStatusDesc", source = "loginStatus", qualifiedByName = "convertLoginStatusDesc")
    MpUserLastLoginIdentityRespVO convert(MpUserLastLoginIdentityDO bean);

    List<MpUserLastLoginIdentityRespVO> convertList(List<MpUserLastLoginIdentityDO> list);

    MpUserLastLoginIdentityDO convert(MpUserLastLoginIdentitySaveReqVO bean);

    @Named("convertIdentityTypeDesc")
    default String convertIdentityTypeDesc(String identityType) {
        if (identityType == null) {
            return null;
        }
        for (IdentityTypeEnum identityTypeEnum : IdentityTypeEnum.values()) {
            if (identityTypeEnum.getType().equals(identityType)) {
                return identityTypeEnum.getDescription();
            }
        }
        return identityType;
    }

    @Named("convertLoginStatusDesc")
    default String convertLoginStatusDesc(Integer loginStatus) {
        if (loginStatus == null) {
            return null;
        }
        for (LoginStatusEnum loginStatusEnum : LoginStatusEnum.values()) {
            if (loginStatusEnum.getStatus().equals(loginStatus)) {
                return loginStatusEnum.getDescription();
            }
        }
        return String.valueOf(loginStatus);
    }

}