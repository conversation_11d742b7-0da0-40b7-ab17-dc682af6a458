package cn.bztmaster.cnt.module.publicbiz.convert.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 考题分类转换类
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface QuestionCategoryConvert {

    QuestionCategoryConvert INSTANCE = Mappers.getMapper(QuestionCategoryConvert.class);

    /**
     * 保存请求VO转换为DO
     *
     * @param bean 保存请求VO
     * @return DO
     */
    QuestionCategoryDO convert(QuestionCategorySaveReqVO bean);

    /**
     * DO转换为响应VO
     *
     * @param bean DO
     * @return 响应VO
     */
    QuestionCategoryRespVO convert(QuestionCategoryDO bean);

    /**
     * DO列表转换为响应VO列表
     *
     * @param list DO列表
     * @return 响应VO列表
     */
    List<QuestionCategoryRespVO> convertList(List<QuestionCategoryDO> list);

    /**
     * 分页结果转换
     *
     * @param page 分页结果
     * @return 响应VO分页结果
     */
    PageResult<QuestionCategoryRespVO> convertPage(PageResult<QuestionCategoryDO> page);

}
