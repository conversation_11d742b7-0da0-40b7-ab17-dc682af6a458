package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 场地类型枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SiteTypeEnum implements ArrayValuable<String> {

    TRAINING_CLASSROOM("培训教室", "用于各类技能培训"),
    MULTI_FUNCTION_HALL("多功能厅", "大型活动、讲座等"),
    MEETING_ROOM("会议室", "会议、研讨等"),
    TRAINING_ROOM("实训室", "实操训练"),
    EXAM_VENUE("考试场地", "考试专用"),
    SEMINAR_ROOM("研讨室", "小型研讨、交流");

    public static final String[] ARRAYS = Arrays.stream(values()).map(SiteTypeEnum::getType).toArray(String[]::new);

    /**
     * 类型值
     */
    private final String type;
    /**
     * 类型描述
     */
    private final String description;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 枚举
     */
    public static SiteTypeEnum getByType(String type) {
        return Arrays.stream(values())
                .filter(item -> item.getType().equals(type))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的场地类型
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValid(String type) {
        return getByType(type) != null;
    }
}
