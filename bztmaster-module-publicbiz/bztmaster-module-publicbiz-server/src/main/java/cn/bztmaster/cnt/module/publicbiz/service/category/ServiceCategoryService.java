package cn.bztmaster.cnt.module.publicbiz.service.category;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.category.ServiceCategoryDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 服务分类 Service 接口
 *
 * <AUTHOR>
 */
public interface ServiceCategoryService {

    /**
     * 创建服务分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid ServiceCategorySaveReqVO createReqVO);

    /**
     * 更新服务分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid ServiceCategorySaveReqVO updateReqVO);

    /**
     * 删除服务分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得服务分类
     *
     * @param id 编号
     * @return 服务分类
     */
    ServiceCategoryDO getCategory(Long id);

    /**
     * 校验服务分类
     *
     * @param id 分类编号
     */
    void validateCategory(Long id);

    /**
     * 获得服务分类的层级
     *
     * @param id 编号
     * @return 服务分类的层级
     */
    Integer getCategoryLevel(Long id);

    /**
     * 获得服务分类列表
     *
     * @param listReqVO 查询条件
     * @return 服务分类列表
     */
    List<ServiceCategoryDO> getCategoryList(ServiceCategoryListReqVO listReqVO);

    /**
     * 获得服务分类分页
     *
     * @param listReqVO 查询条件
     * @return 服务分类分页
     */
    PageResult<ServiceCategoryDO> getCategoryPage(ServiceCategoryListReqVO listReqVO);

    /**
     * 获得开启状态的服务分类列表
     *
     * @return 服务分类列表
     */
    List<ServiceCategoryDO> getEnableCategoryList();

    /**
     * 获得开启状态的服务分类列表，指定编号
     *
     * @param ids 服务分类编号数组
     * @return 服务分类列表
     */
    List<ServiceCategoryDO> getEnableCategoryList(List<Long> ids);

    /**
     * 校验服务分类是否有效。如下情况，视为无效：
     * 1. 服务分类编号不存在
     * 2. 服务分类被禁用
     * 3. 服务分类层级校验，必须使用第二级的服务分类及以下
     *
     * @param ids 服务分类编号数组
     */
    void validateCategoryList(Collection<Long> ids);

} 