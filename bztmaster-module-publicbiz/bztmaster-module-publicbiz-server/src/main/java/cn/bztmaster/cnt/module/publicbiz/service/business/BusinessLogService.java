package cn.bztmaster.cnt.module.publicbiz.service.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessLogDO;

import java.util.Collection;
import java.util.List;

public interface BusinessLogService {
    PageResult<BusinessLogRespVO> getBusinessLogPage(BusinessLogPageReqVO reqVO);
    Long createBusinessLog(BusinessLogSaveReqVO reqVO);
    BusinessLogRespVO getBusinessLogDetail(Long id);

    BusinessLogDO getBusinessLog(Long id);

    List<BusinessLogDO> getBusinessLogList(Collection<Long> ids);
}