package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 场地状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SiteStatusEnum implements ArrayValuable<String> {

    AVAILABLE("可用", "正常可预约使用"),
    RESERVED("已预约", "已被预约占用"),
    MAINTENANCE("维护中", "设备维护，暂不可用"),
    DISABLED("停用", "停止使用");

    public static final String[] ARRAYS = Arrays.stream(values()).map(SiteStatusEnum::getStatus).toArray(String[]::new);

    /**
     * 状态值
     */
    private final String status;
    /**
     * 状态描述
     */
    private final String description;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static SiteStatusEnum getByStatus(String status) {
        return Arrays.stream(values())
                .filter(item -> item.getStatus().equals(status))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的场地状态
     *
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(String status) {
        return getByStatus(status) != null;
    }

    /**
     * 判断是否可预约
     *
     * @param status 状态值
     * @return 是否可预约
     */
    public static boolean isBookable(String status) {
        return AVAILABLE.getStatus().equals(status);
    }
}
