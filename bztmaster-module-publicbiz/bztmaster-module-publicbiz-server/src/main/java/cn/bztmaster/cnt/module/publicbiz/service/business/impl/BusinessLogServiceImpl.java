package cn.bztmaster.cnt.module.publicbiz.service.business.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.business.BusinessLogMapper;
import cn.bztmaster.cnt.module.publicbiz.service.business.BusinessLogService;
import cn.bztmaster.cnt.module.publicbiz.convert.business.BusinessLogConvert;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Collection;
import java.util.Collections;

@Service
public class BusinessLogServiceImpl implements BusinessLogService {
    @Resource
    private BusinessLogMapper businessLogMapper;
    @Resource
    private BusinessLogConvert businessLogConvert;

    @Override
    public PageResult<BusinessLogRespVO> getBusinessLogPage(BusinessLogPageReqVO reqVO) {
        PageResult<BusinessLogDO> pageResult = businessLogMapper.selectPage(reqVO);
        return businessLogConvert.convertPage(pageResult);
    }

    @Override
    @Transactional
    public Long createBusinessLog(BusinessLogSaveReqVO reqVO) {
        BusinessLogDO log = businessLogConvert.convert(reqVO);
        log.setCreateTime(new Date());
        log.setDeleted(false);
        businessLogMapper.insert(log);
        return log.getId();
    }

    @Override
    public BusinessLogRespVO getBusinessLogDetail(Long id) {
        BusinessLogDO log = businessLogMapper.selectById(id);
        if (log == null || Boolean.TRUE.equals(log.getDeleted())) {
            return null;
        }
        return businessLogConvert.convert(log);
    }

    @Override
    public BusinessLogDO getBusinessLog(Long id) {
        return businessLogMapper.selectById(id);
    }

    @Override
    public List<BusinessLogDO> getBusinessLogList(Collection<Long> ids) {
        return (ids == null || ids.isEmpty()) ? Collections.emptyList() : businessLogMapper.selectBatchIds(ids);
    }
} 