package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AuntPunchRecordDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AuntPunchRecordMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntPunchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿姨打卡 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntPunchServiceImpl implements AuntPunchService {

    @Resource
    private AuntPunchRecordMapper auntPunchRecordMapper;

    @Override
    public void recordPunch(AuntPunchRecordReqVO reqVO) {
        // 1. 创建打卡记录
        AuntPunchRecordDO punchRecord = new AuntPunchRecordDO();
        punchRecord.setScheduleId(reqVO.getScheduleId());
        punchRecord.setAuntOneid(reqVO.getAuntOneId());
        punchRecord.setPunchType(reqVO.getPunchType());
        punchRecord.setPunchTime(new Date());
        punchRecord.setPunchLocation(reqVO.getPunchLocation());
        punchRecord.setPunchLatitude(reqVO.getPunchLatitude());
        punchRecord.setPunchLongitude(reqVO.getPunchLongitude());
        punchRecord.setPhotoCount(reqVO.getPhotoCount());
        punchRecord.setPhotoUrls(reqVO.getPhotoUrls());
        punchRecord.setRemark(reqVO.getRemark());
        punchRecord.setCreateTime(new Date());
        punchRecord.setDeleted(false);
        
        // 2. 保存打卡记录
        auntPunchRecordMapper.insert(punchRecord);
        
        log.info("阿姨打卡记录已保存，auntOneId: {}, scheduleId: {}, punchType: {}", 
                reqVO.getAuntOneId(), reqVO.getScheduleId(), reqVO.getPunchType());
    }

    @Override
    public AuntPunchRecordRespVO getPunchRecordList(String auntOneId, String scheduleId, String startDate, String endDate) {
        // 1. 查询打卡记录列表
        List<AuntPunchRecordDO> recordList = auntPunchRecordMapper.selectByAuntOneidAndScheduleId(
                auntOneId, scheduleId, startDate, endDate);
        
        // 2. 转换为VO对象
        List<AuntPunchRecordRespVO.PunchRecord> punchRecords = recordList.stream()
                .map(this::convertToPunchRecordVO)
                .collect(Collectors.toList());
        
        // 3. 组装返回数据
        AuntPunchRecordRespVO respVO = new AuntPunchRecordRespVO();
        respVO.setPunchRecords(punchRecords);
        
        return respVO;
    }

    /**
     * 转换为打卡记录VO
     */
    private AuntPunchRecordRespVO.PunchRecord convertToPunchRecordVO(AuntPunchRecordDO record) {
        AuntPunchRecordRespVO.PunchRecord vo = new AuntPunchRecordRespVO.PunchRecord();
        vo.setId(record.getId());
        vo.setScheduleId(record.getScheduleId());
        vo.setAuntOneId(record.getAuntOneid());
        vo.setAuntName(record.getAuntName());
        vo.setPunchType(record.getPunchType());
        vo.setPunchTime(record.getPunchTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        vo.setPunchLocation(record.getPunchLocation());
        vo.setPunchLatitude(record.getPunchLatitude());
        vo.setPunchLongitude(record.getPunchLongitude());
        vo.setPhotoCount(record.getPhotoCount());
        vo.setPhotoUrls(record.getPhotoUrls());
        vo.setRemark(record.getRemark());
        
        return vo;
    }
} 