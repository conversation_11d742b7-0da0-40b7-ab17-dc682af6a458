package cn.bztmaster.cnt.module.publicbiz.service.siteManagement;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.siteManagement.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.siteManagement.SiteAppointmentDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 场地预约 Service 接口
 *
 * <AUTHOR>
 */
public interface SiteAppointmentService {

    /**
     * 创建预约
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSiteAppointment(@Valid SiteAppointmentSaveReqVO createReqVO);

    /**
     * 更新预约
     *
     * @param updateReqVO 更新信息
     */
    void updateSiteAppointment(@Valid SiteAppointmentSaveReqVO updateReqVO);

    /**
     * 删除预约
     *
     * @param id 编号
     */
    void deleteSiteAppointment(Long id);

    /**
     * 取消预约
     *
     * @param id 编号
     */
    void cancelSiteAppointment(Long id);

    /**
     * 获得预约
     *
     * @param id 编号
     * @return 预约
     */
    SiteAppointmentDO getSiteAppointment(Long id);

    /**
     * 获得预约分页
     *
     * @param pageReqVO 分页查询
     * @return 预约分页
     */
    PageResult<SiteAppointmentDO> getSiteAppointmentPage(SiteAppointmentPageReqVO pageReqVO);

    /**
     * 获得预约列表
     *
     * @param listReqVO 查询条件
     * @return 预约列表
     */
    List<SiteAppointmentDO> getSiteAppointmentList(SiteAppointmentListReqVO listReqVO);

    /**
     * 根据场地和日期查询预约列表
     *
     * @param reqVO 查询条件
     * @return 预约列表
     */
    List<SiteAppointmentDO> getSiteAppointmentListBySiteAndDate(SiteAppointmentBySiteAndDateReqVO reqVO);

    /**
     * 校验预约存在
     *
     * @param id 编号
     * @return 预约信息
     */
    SiteAppointmentDO validateSiteAppointmentExists(Long id);

    /**
     * 校验预约时间冲突
     *
     * @param createReqVO 预约信息
     * @param excludeId 排除的预约ID（用于更新时排除自己）
     */
    void validateSiteAppointmentTimeConflict(SiteAppointmentSaveReqVO createReqVO, Long excludeId);

    /**
     * 校验预约是否可取消
     *
     * @param id 编号
     * @return 预约信息
     */
    SiteAppointmentDO validateSiteAppointmentCancellable(Long id);

}
