package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate;

import cn.bztmaster.cnt.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 证书模板 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_certificate_template")
@KeySequence("publicbiz_certificate_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
public class CertificateTemplateDO extends TenantBaseDO {

    /**
     * 主键，自增
     */
    @TableId
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 证书类型：training-培训证书，completion-结业证书，skill-技能证书
     */
    private String type;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 背景图片文件名
     */
    private String background;

    /**
     * 背景图片完整URL地址
     */
    private String backgroundUrl;

    /**
     * 适用课程id
     */
    private String course;

    /**
     * 适用课程名称
     */
    private String courseName;

    /**
     * 状态：draft-草稿，active-启用中，inactive-已停用
     */
    private String status;

    /**
     * HTML模板内容，支持占位符变量
     */
    private String htmlContent;

    /**
     * 模板预览图URL
     */
    private String previewUrl;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 字段配置列表（非数据库字段，用于查询时组装数据）
     */
    @TableField(exist = false)
    private List<CertificateTemplateFieldDO> fields;

}