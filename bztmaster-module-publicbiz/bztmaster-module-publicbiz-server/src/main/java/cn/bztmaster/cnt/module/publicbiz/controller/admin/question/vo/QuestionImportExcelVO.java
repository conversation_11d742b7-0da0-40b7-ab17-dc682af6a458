package cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 考题 Excel/CSV 导入 VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class QuestionImportExcelVO {

    @ExcelProperty("一级名称")
    private String level1Name;

    @ExcelProperty("一级代码")
    private String level1Code;

    @ExcelProperty("二级名称")
    private String level2Name;

    @ExcelProperty("二级代码")
    private String level2Code;

    @ExcelProperty("三级名称")
    private String level3Name;

    @ExcelProperty("三级代码")
    private String level3Code;

    @ExcelProperty("认定点名称")
    private String certName;

    @ExcelProperty("认定点代码")
    private String certCode;

    @ExcelProperty("题型")
    private String type;

    @ExcelProperty("题干")
    private String title;

    @ExcelProperty("选择项")
    private String options;

    @ExcelProperty("参考答案")
    private String answer;

}
