package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 阿姨订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntOrderServiceImpl implements AuntOrderService {

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Override
    public PageResult<AuntOrderListRespVO> getOrderList(AuntOrderListReqVO reqVO) {
        // 1. 根据阿姨OneID查询订单列表
        List<DomesticOrderDO> orderList = domesticOrderMapper.selectByPractitionerOneid(reqVO.getAuntOneId());
        
        // 2. 根据查询条件过滤
        List<DomesticOrderDO> filteredList = orderList.stream()
                .filter(order -> {
                    // 按订单状态过滤
                    if (reqVO.getOrderStatus() != null && !reqVO.getOrderStatus().isEmpty()) {
                        // 这里需要根据实际的订单状态字段来过滤
                        // 暂时返回true，实际应该根据order_status字段过滤
                        return true;
                    }
                    // 按客户姓名过滤
                    if (reqVO.getCustomerName() != null && !reqVO.getCustomerName().isEmpty()) {
                        return order.getCustomerName().contains(reqVO.getCustomerName());
                    }
                    // 按服务地址过滤
                    if (reqVO.getServiceAddress() != null && !reqVO.getServiceAddress().isEmpty()) {
                        return order.getServiceAddress().contains(reqVO.getServiceAddress());
                    }
                    return true;
                })
                .collect(Collectors.toList());

        // 3. 转换为VO对象
        List<AuntOrderListRespVO> voList = filteredList.stream()
                .map(this::convertToOrderListRespVO)
                .collect(Collectors.toList());

        // 4. 分页处理
        int total = voList.size();
        int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, total);
        
        List<AuntOrderListRespVO> pageList = voList.subList(start, end);

        return new PageResult<>(pageList, (long) total);
    }

    @Override
    public AuntOrderDetailRespVO getOrderDetail(Long orderId, String auntOneId) {
        // 1. 查询订单详情
        DomesticOrderDO order = domesticOrderMapper.selectByOrderId(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return null;
        }

        // 2. 验证订单是否属于该阿姨
        if (!auntOneId.equals(order.getPractitionerOneid())) {
            log.error("订单不属于该阿姨，orderId: {}, auntOneId: {}", orderId, auntOneId);
            return null;
        }

        // 3. 转换为详情VO
        return convertToOrderDetailRespVO(order);
    }

    @Override
    public void confirmOrder(Long orderId, String auntOneId) {
        // 1. 查询订单详情
        DomesticOrderDO order = domesticOrderMapper.selectByOrderId(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            throw new RuntimeException("订单不存在");
        }

        // 2. 验证订单是否属于该阿姨
        if (!auntOneId.equals(order.getPractitionerOneid())) {
            log.error("订单不属于该阿姨，orderId: {}, auntOneId: {}", orderId, auntOneId);
            throw new RuntimeException("订单不属于该阿姨");
        }

        // 3. 更新订单状态为已确认
        // TODO: 这里需要根据实际的订单状态字段来更新
        // 暂时只是记录日志，实际应该更新数据库
        log.info("阿姨确认订单，orderId: {}, auntOneId: {}", orderId, auntOneId);
    }

    /**
     * 转换为订单列表响应VO
     */
    private AuntOrderListRespVO convertToOrderListRespVO(DomesticOrderDO order) {
        AuntOrderListRespVO vo = new AuntOrderListRespVO();
        vo.setOrderId(order.getOrderId());
        vo.setOrderNo(order.getOrderNo());
        vo.setCustomerName(order.getCustomerName());
        vo.setCustomerPhone(order.getCustomerPhone());
        vo.setServiceAddress(order.getServiceAddress());
        vo.setServicePackageName(order.getServicePackageName());
        
        // 转换日期
        if (order.getServiceStartDate() != null) {
            vo.setServiceStartDate(order.getServiceStartDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        if (order.getServiceEndDate() != null) {
            vo.setServiceEndDate(order.getServiceEndDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        
        vo.setServiceDuration(order.getServiceDuration());
        vo.setServiceAmount(order.getActualAmount());
        vo.setOrderStatus("pending"); // 暂时返回固定状态，实际应该从数据库获取
        vo.setCreateTime(order.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        vo.setAgencyName(order.getAgencyName());
        
        return vo;
    }

    /**
     * 转换为订单详情响应VO
     */
    private AuntOrderDetailRespVO convertToOrderDetailRespVO(DomesticOrderDO order) {
        AuntOrderDetailRespVO vo = new AuntOrderDetailRespVO();
        vo.setOrderId(order.getOrderId());
        vo.setOrderNo(order.getOrderNo());
        vo.setOrderStatus("pending"); // 暂时返回固定状态，实际应该从数据库获取
        vo.setPaymentStatus("paid"); // 暂时返回固定状态，实际应该从数据库获取
        vo.setSettlementStatus("pending"); // 暂时返回固定状态，实际应该从数据库获取
        vo.setCreateTime(order.getCreateTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime());
        vo.setTotalAmount(order.getActualAmount());
        vo.setPractitionerIncome(order.getServiceFee());
        vo.setPlatformIncome(order.getPlatformFee());
        vo.setAgencyName(order.getAgencyName());
        
        // 设置客户信息
        AuntOrderDetailRespVO.CustomerInfo customerInfo = new AuntOrderDetailRespVO.CustomerInfo();
        customerInfo.setCustomerName(order.getCustomerName());
        customerInfo.setCustomerPhone(order.getCustomerPhone());
        customerInfo.setServiceAddress(order.getServiceAddress());
        customerInfo.setCustomerRemark(order.getCustomerRemark());
        vo.setCustomerInfo(customerInfo);
        
        // 设置服务信息
        AuntOrderDetailRespVO.ServiceInfo serviceInfo = new AuntOrderDetailRespVO.ServiceInfo();
        serviceInfo.setServicePackageName(order.getServicePackageName());
        serviceInfo.setServicePackagePrice(order.getServicePackagePrice());
        if (order.getServiceStartDate() != null) {
            serviceInfo.setServiceStartDate(order.getServiceStartDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        if (order.getServiceEndDate() != null) {
            serviceInfo.setServiceEndDate(order.getServiceEndDate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDate());
        }
        serviceInfo.setServiceDuration(order.getServiceDuration());
        serviceInfo.setServiceDescription(order.getServiceDescription());
        serviceInfo.setServiceDetails(order.getServiceDetails());
        serviceInfo.setServiceProcess(order.getServiceProcess());
        vo.setServiceInfo(serviceInfo);
        
        return vo;
    }
} 