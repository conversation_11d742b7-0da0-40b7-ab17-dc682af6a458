package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerRatingRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PractitionerRatingRecordConvert {
    PractitionerRatingRecordConvert INSTANCE = Mappers.getMapper(PractitionerRatingRecordConvert.class);

    PractitionerRatingRecordRespVO convert(PractitionerRatingRecordDO bean);

    List<PractitionerRatingRecordRespVO> convertList(List<PractitionerRatingRecordDO> list);
} 