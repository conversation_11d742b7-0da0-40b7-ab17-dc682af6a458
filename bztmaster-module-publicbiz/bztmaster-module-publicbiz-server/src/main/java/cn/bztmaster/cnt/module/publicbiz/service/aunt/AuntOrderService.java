package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListRespVO;

/**
 * 阿姨订单 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntOrderService {

    /**
     * 获得阿姨订单列表
     *
     * @param reqVO 查询条件
     * @return 订单列表
     */
    PageResult<AuntOrderListRespVO> getOrderList(AuntOrderListReqVO reqVO);

    /**
     * 获得阿姨订单详情
     *
     * @param orderId 订单ID
     * @param auntOneId 阿姨OneID
     * @return 订单详情
     */
    AuntOrderDetailRespVO getOrderDetail(Long orderId, String auntOneId);

    /**
     * 确认订单
     *
     * @param orderId 订单ID
     * @param auntOneId 阿姨OneID
     */
    void confirmOrder(Long orderId, String auntOneId);

} 