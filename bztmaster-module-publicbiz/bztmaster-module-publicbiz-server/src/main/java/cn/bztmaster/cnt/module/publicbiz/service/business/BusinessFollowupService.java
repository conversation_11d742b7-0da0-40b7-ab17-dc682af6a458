package cn.bztmaster.cnt.module.publicbiz.service.business;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.business.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.business.BusinessFollowupDO;

import java.util.Collection;
import java.util.List;

public interface BusinessFollowupService {
    PageResult<BusinessFollowupRespVO> getBusinessFollowupPage(BusinessFollowupPageReqVO reqVO);
    Long createBusinessFollowup(BusinessFollowupSaveReqVO reqVO);
    BusinessFollowupRespVO getBusinessFollowupDetail(Long id);

    BusinessFollowupDO getBusinessFollowup(Long id);

    List<BusinessFollowupDO> getBusinessFollowupList(Collection<Long> ids);
}