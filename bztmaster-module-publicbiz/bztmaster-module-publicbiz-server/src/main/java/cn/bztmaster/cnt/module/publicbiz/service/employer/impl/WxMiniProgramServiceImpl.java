package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.WxCode2SessionRespVO;
import cn.bztmaster.cnt.module.publicbiz.framework.config.WxMiniProgramConfig;
import cn.bztmaster.cnt.module.publicbiz.service.employer.WxMiniProgramService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WxMiniProgramServiceImpl implements WxMiniProgramService {

    @Resource
    private WxMiniProgramConfig wxMiniProgramConfig;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public WxCode2SessionRespVO code2Session(String code) {
        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("appid", wxMiniProgramConfig.getAppId());
            params.put("secret", wxMiniProgramConfig.getSecret());
            params.put("js_code", code);
            params.put("grant_type", "authorization_code");

            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(wxMiniProgramConfig.getCode2SessionUrl());
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            String url = urlBuilder.substring(0, urlBuilder.length() - 1);

            log.info("调用微信登录接口，URL: {}", url);

            // 发送HTTP请求
            String response = restTemplate.getForObject(url, String.class);
            log.info("微信登录接口响应: {}", response);

            // 解析响应
            WxCode2SessionRespVO result = objectMapper.readValue(response, WxCode2SessionRespVO.class);

            if (!result.isSuccess()) {
                log.error("微信登录失败，错误码: {}, 错误信息: {}", result.getErrcode(), result.getErrmsg());
            }

            return result;

        } catch (Exception e) {
            log.error("调用微信登录接口异常", e);
            WxCode2SessionRespVO errorResult = new WxCode2SessionRespVO();
            errorResult.setErrcode(-1);
            errorResult.setErrmsg("调用微信接口异常: " + e.getMessage());
            return errorResult;
        }
    }

}