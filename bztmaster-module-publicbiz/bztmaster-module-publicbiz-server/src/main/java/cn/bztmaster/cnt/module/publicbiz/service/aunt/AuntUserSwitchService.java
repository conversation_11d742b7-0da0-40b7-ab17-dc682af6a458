package cn.bztmaster.cnt.module.publicbiz.service.aunt;

import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchRespVO;

/**
 * 阿姨用户切换 Service 接口
 *
 * <AUTHOR>
 */
public interface AuntUserSwitchService {

    /**
     * 根据openId或手机号查询用户是否注册了家政人员信息
     *
     * @param reqVO 查询请求参数
     * @return 家政人员信息，如果未注册则返回isRegistered=false
     */
    AuntUserSwitchRespVO checkPractitionerRegistration(AuntUserSwitchReqVO reqVO);

}
