package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.digitalAsset;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程章节表 DO（线上课程专用）
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_course_chapter")
@Schema(description = "课程章节表 DO")
public class CourseChapterDO extends BaseDO {

    /**
     * 章节ID
     */
    @TableId
    @Schema(description = "章节ID", example = "1")
    private Long id;

    /**
     * 课程ID
     */
    @Schema(description = "课程ID", example = "123")
    private Long courseId;

    /**
     * 章节标题
     */
    @Schema(description = "章节标题", example = "SWOT分析法基础")
    private String title;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号", example = "1")
    private Integer sortOrder;
}
