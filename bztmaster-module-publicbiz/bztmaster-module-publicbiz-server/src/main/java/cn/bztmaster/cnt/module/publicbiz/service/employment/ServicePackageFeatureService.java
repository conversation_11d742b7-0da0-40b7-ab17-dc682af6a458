package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageFeatureSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO;

import javax.validation.Valid;

public interface ServicePackageFeatureService {
    Long createServicePackageFeature(@Valid ServicePackageFeatureSaveReqVO createReqVO);

    void deleteServicePackageFeature(Long id);
} 