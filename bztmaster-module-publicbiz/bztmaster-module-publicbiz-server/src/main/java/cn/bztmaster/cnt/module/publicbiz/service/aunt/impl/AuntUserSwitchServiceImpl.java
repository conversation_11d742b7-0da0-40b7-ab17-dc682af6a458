package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.hutool.core.util.StrUtil;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntUserSwitchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 阿姨用户切换 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntUserSwitchServiceImpl implements AuntUserSwitchService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public AuntUserSwitchRespVO checkPractitionerRegistration(AuntUserSwitchReqVO reqVO) {
        log.info("检查用户是否注册家政人员信息，openId: {}, mobile: {}", reqVO.getOpenId(), reqVO.getMobile());
        
        PractitionerDO practitioner = null;
        
        // 优先使用openId查询
        if (StrUtil.isNotBlank(reqVO.getOpenId())) {
            practitioner = practitionerMapper.selectByOpenId(reqVO.getOpenId());
            log.info("根据openId查询结果: {}", practitioner != null ? "找到家政人员" : "未找到家政人员");
        }
        
        // 如果openId查询无结果，且提供了手机号，则使用手机号查询
        if (practitioner == null && StrUtil.isNotBlank(reqVO.getMobile())) {
            practitioner = practitionerMapper.selectByMobile(reqVO.getMobile());
            log.info("根据手机号查询结果: {}", practitioner != null ? "找到家政人员" : "未找到家政人员");
        }
        
        // 构建响应结果
        AuntUserSwitchRespVO respVO = new AuntUserSwitchRespVO();
        
        if (practitioner != null) {
            // 用户已注册家政人员信息
            respVO.setIsRegistered(true);
            respVO.setAuntOneId(practitioner.getAuntOneid());
            respVO.setName(practitioner.getName());
            respVO.setPhone(practitioner.getPhone());
            respVO.setServiceType(practitioner.getServiceType());
            respVO.setExperienceYears(practitioner.getExperienceYears());
            respVO.setPlatformStatus(practitioner.getPlatformStatus());
            respVO.setRating(practitioner.getRating());
            respVO.setStatus(practitioner.getStatus());
            respVO.setCurrentStatus(practitioner.getCurrentStatus());
            
            log.info("用户已注册家政人员信息，auntOneId: {}, name: {}", practitioner.getAuntOneid(), practitioner.getName());
        } else {
            // 用户未注册家政人员信息
            respVO.setIsRegistered(false);
            log.info("用户未注册家政人员信息");
        }
        
        return respVO;
    }

}
