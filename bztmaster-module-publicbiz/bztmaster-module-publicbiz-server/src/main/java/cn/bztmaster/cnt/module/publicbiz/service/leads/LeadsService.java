package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 线索 Service 接口
 *
 * <AUTHOR>
 */
public interface LeadsService {

    /**
     * 创建线索
     *
     * @param createReqDTO 创建线索请求
     * @return 线索编号
     */
    Long createLead(@Valid LeadsSaveReqDTO createReqDTO);

    /**
     * 更新线索
     *
     * @param updateReqDTO 更新线索请求
     */
    void updateLead(@Valid LeadsSaveReqDTO updateReqDTO);

    /**
     * 删除线索
     *
     * @param id 线索编号
     */
    void deleteLead(Long id);

    /**
     * 获取线索
     *
     * @param id 线索编号
     * @return 线索
     */
    LeadInfoDO getLead(Long id);

    /**
     * 根据线索ID获取线索
     *
     * @param leadId 线索ID字符串
     * @return 线索
     */
    LeadInfoDO getLeadByLeadId(String leadId);

    /**
     * 获取线索列表
     *
     * @param listReqDTO 列表查询参数
     * @return 线索列表
     */
    List<LeadInfoDO> getLeadList(LeadsListReqDTO listReqDTO);

    /**
     * 获取线索分页
     *
     * @param pageReqDTO 分页查询参数
     * @return 线索分页
     */
    PageResult<LeadInfoDO> getLeadPage(LeadsPageReqDTO pageReqDTO);

    /**
     * 分配线索
     *
     * @param assignReqDTO 分配线索请求
     */
    void assignLead(@Valid LeadsAssignReqDTO assignReqDTO);
}