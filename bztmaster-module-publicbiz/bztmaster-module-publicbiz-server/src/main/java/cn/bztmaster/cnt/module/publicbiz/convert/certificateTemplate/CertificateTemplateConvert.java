package cn.bztmaster.cnt.module.publicbiz.convert.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.collection.CollectionUtils;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateFieldRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.certificateTemplate.dto.CertificateTemplateFieldSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateFieldRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateFieldSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateFieldDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 证书模板 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CertificateTemplateConvert {

    CertificateTemplateConvert INSTANCE = Mappers.getMapper(CertificateTemplateConvert.class);

    /**
     * 转换 DO 到 VO
     *
     * @param bean DO对象
     * @return VO对象
     */
    @Mapping(source = "fields", target = "fields", qualifiedByName = "convertFieldListToVO")
    CertificateTemplateRespVO convert(CertificateTemplateDO bean);

    /**
     * 转换 DO 列表到 VO 列表
     *
     * @param list DO列表
     * @return VO列表
     */
    List<CertificateTemplateRespVO> convertList(List<CertificateTemplateDO> list);

    /**
     * 转换 DO 分页到 VO 分页
     *
     * @param page DO分页
     * @return VO分页
     */
    PageResult<CertificateTemplateRespVO> convertPage(PageResult<CertificateTemplateDO> page);

    /**
     * 转换 DO 到 DTO
     *
     * @param bean DO对象
     * @return DTO对象
     */
    @Mapping(source = "fields", target = "fields", qualifiedByName = "convertFieldListToDTO")
    CertificateTemplateRespDTO convertToDTO(CertificateTemplateDO bean);

    /**
     * 转换 DO 列表到 DTO 列表
     *
     * @param list DO列表
     * @return DTO列表
     */
    List<CertificateTemplateRespDTO> convertToDTOList(List<CertificateTemplateDO> list);

    /**
     * 转换字段 DO 到 VO
     *
     * @param bean 字段DO对象
     * @return 字段VO对象
     */
    CertificateTemplateFieldRespVO convertField(CertificateTemplateFieldDO bean);

    /**
     * 转换字段 DO 列表到 VO 列表
     *
     * @param list 字段DO列表
     * @return 字段VO列表
     */
    @Named("convertFieldListToVO")
    List<CertificateTemplateFieldRespVO> convertFieldListToVO(List<CertificateTemplateFieldDO> list);

    /**
     * 转换字段 DO 到 DTO
     *
     * @param bean 字段DO对象
     * @return 字段DTO对象
     */
    CertificateTemplateFieldRespDTO convertFieldToDTO(CertificateTemplateFieldDO bean);

    /**
     * 转换字段 DO 列表到 DTO 列表
     *
     * @param list 字段DO列表
     * @return 字段DTO列表
     */
    @Named("convertFieldListToDTO")
    List<CertificateTemplateFieldRespDTO> convertFieldToDTOList(List<CertificateTemplateFieldDO> list);

    /**
     * 转换字段保存请求 VO 到 DO
     *
     * @param bean 字段保存请求VO
     * @return 字段DO对象
     */
    @Mapping(source = "fieldId", target = "fieldId")
    CertificateTemplateFieldDO convertField(CertificateTemplateFieldSaveReqVO bean);

    /**
     * 转换字段保存请求 VO 列表到 DO 列表
     *
     * @param list 字段保存请求VO列表
     * @return 字段DO列表
     */
    List<CertificateTemplateFieldDO> convertFieldListFromVO(List<CertificateTemplateFieldSaveReqVO> list);

    /**
     * 转换字段保存请求 DTO 到 DO
     *
     * @param bean 字段保存请求DTO
     * @return 字段DO对象
     */
    @Mapping(source = "fieldId", target = "fieldId")
    CertificateTemplateFieldDO convertFieldFromDTO(CertificateTemplateFieldSaveReqDTO bean);

    /**
     * 转换字段保存请求 DTO 列表到 DO 列表
     *
     * @param list 字段保存请求DTO列表
     * @return 字段DO列表
     */
    List<CertificateTemplateFieldDO> convertFieldFromDTOList(List<CertificateTemplateFieldSaveReqDTO> list);

}