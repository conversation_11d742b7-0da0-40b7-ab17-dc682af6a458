package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 题型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionTypeEnum {

    SINGLE_CHOICE("单选题", "单项选择题", "A"),
    MULTIPLE_CHOICE("多选题", "多项选择题", "A,C,D"),
    TRUE_FALSE("判断题", "判断题", "正确"),
    SHORT_ANSWER("简答题", "简答题", "详细文字描述"),
    FILL_BLANK("填空题", "填空题", "答案1|答案2|答案3"),
    MATERIAL("材料题", "材料题", "详细文字描述"),
    SORT("排序题", "排序题", "C,A,B,D"),
    MATCH("匹配题", "匹配题", "1-A,2-B,3-C"),
    FILE_UPLOAD("文件上传题", "文件上传题", "评分标准和文件要求说明");

    /**
     * 题型值
     */
    private final String value;

    /**
     * 题型说明
     */
    private final String description;

    /**
     * 答案格式示例
     */
    private final String answerExample;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static QuestionTypeEnum getByValue(String value) {
        for (QuestionTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 校验题型是否有效
     *
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(String value) {
        return getByValue(value) != null;
    }

}
