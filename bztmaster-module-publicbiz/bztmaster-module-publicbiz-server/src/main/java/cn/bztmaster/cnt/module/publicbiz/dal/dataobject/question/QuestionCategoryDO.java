package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 考题分类表 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_question_category")
@Schema(description = "考题分类表 DO")
public class QuestionCategoryDO extends BaseDO {

    /**
     * 主键，自增
     */
    @TableId
    @Schema(description = "主键，自增")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 一级分类名称
     */
    @Schema(description = "一级分类名称")
    private String level1Name;

    /**
     * 一级分类代码
     */
    @Schema(description = "一级分类代码")
    private String level1Code;

    /**
     * 二级分类名称
     */
    @Schema(description = "二级分类名称")
    private String level2Name;

    /**
     * 二级分类代码
     */
    @Schema(description = "二级分类代码")
    private String level2Code;

    /**
     * 三级分类名称
     */
    @Schema(description = "三级分类名称")
    private String level3Name;

    /**
     * 三级分类代码
     */
    @Schema(description = "三级分类代码")
    private String level3Code;

    /**
     * 认定点名称
     */
    @Schema(description = "认定点名称")
    private String certName;

    /**
     * 认定点代码
     */
    @Schema(description = "认定点代码")
    private String certCode;

    /**
     * 业务模块：家政业务、高校业务、培训业务、认证业务
     */
    @Schema(description = "业务模块")
    private String biz;

    /**
     * 业务模块名称
     */
    @Schema(description = "业务模块名称")
    private String bizName;

    /**
     * 父级分类ID，0表示顶级分类
     */
    @Schema(description = "父级分类ID")
    private Long parentId;

    /**
     * 分类层级：1-一级，2-二级，3-三级
     */
    @Schema(description = "分类层级")
    private Integer level;

    /**
     * 排序序号
     */
    @Schema(description = "排序序号")
    private Integer sortOrder;

    /**
     * 分类描述
     */
    @Schema(description = "分类描述")
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String creatorName;

}
