package cn.bztmaster.cnt.module.publicbiz.service.aunt.impl;

import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntWorkbenchInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntWorkbenchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 阿姨工作台 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuntWorkbenchServiceImpl implements AuntWorkbenchService {

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public AuntWorkbenchInfoRespVO getWorkbenchInfo(String auntOneId) {
        // 1. 查询阿姨基本信息
        PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(auntOneId);
        if (practitioner == null) {
            log.error("阿姨信息不存在，auntOneId: {}", auntOneId);
            return null;
        }

        // 2. 构建阿姨信息
        AuntWorkbenchInfoRespVO.AuntInfo auntInfo = buildAuntInfo(practitioner);

        // 3. 构建KPI数据
        AuntWorkbenchInfoRespVO.KPIData kpiData = buildKPIData(auntOneId);

        // 4. 构建订单统计
        AuntWorkbenchInfoRespVO.OrderStats orderStats = buildOrderStats(auntOneId);

        // 5. 组装返回数据
        AuntWorkbenchInfoRespVO workbenchInfo = new AuntWorkbenchInfoRespVO();
        workbenchInfo.setAuntInfo(auntInfo);
        workbenchInfo.setKpiData(kpiData);
        workbenchInfo.setOrderStats(orderStats);

        return workbenchInfo;
    }

    /**
     * 构建阿姨信息
     */
    private AuntWorkbenchInfoRespVO.AuntInfo buildAuntInfo(PractitionerDO practitioner) {
        AuntWorkbenchInfoRespVO.AuntInfo auntInfo = new AuntWorkbenchInfoRespVO.AuntInfo();
        auntInfo.setOneId(practitioner.getAuntOneid());
        auntInfo.setName(practitioner.getName());
        auntInfo.setPhone(practitioner.getPhone());
        auntInfo.setAvatar(practitioner.getAvatar());
        auntInfo.setServiceType(practitioner.getServiceType());
        auntInfo.setExperienceYears(practitioner.getExperienceYears());
        auntInfo.setRating(practitioner.getRating());
        auntInfo.setCurrentStatus(practitioner.getCurrentStatus());
        auntInfo.setAgencyName(practitioner.getAgencyName());
        return auntInfo;
    }

    /**
     * 构建KPI数据
     */
    private AuntWorkbenchInfoRespVO.KPIData buildKPIData(String auntOneId) {
        AuntWorkbenchInfoRespVO.KPIData kpiData = new AuntWorkbenchInfoRespVO.KPIData();
        
        // 获取今日排班数
        Integer todaySchedule = getTodayScheduleCount(auntOneId);
        kpiData.setTodaySchedule(todaySchedule);
        
        // 获取本月预估收入
        BigDecimal monthlyIncome = getMonthlyIncome(auntOneId);
        kpiData.setMonthlyIncome(monthlyIncome);
        
        // 获取本月服务时长
        Integer monthlyServiceHours = getMonthlyServiceHours(auntOneId);
        kpiData.setMonthlyServiceHours(monthlyServiceHours);
        
        return kpiData;
    }

    /**
     * 构建订单统计
     */
    private AuntWorkbenchInfoRespVO.OrderStats buildOrderStats(String auntOneId) {
        AuntWorkbenchInfoRespVO.OrderStats orderStats = new AuntWorkbenchInfoRespVO.OrderStats();
        
        // 获取各状态订单数量
        Integer pending = getOrderCountByStatus(auntOneId, "pending");
        Integer inProgress = getOrderCountByStatus(auntOneId, "in_progress");
        Integer completed = getOrderCountByStatus(auntOneId, "completed");
        Integer cancelled = getOrderCountByStatus(auntOneId, "cancelled");
        
        orderStats.setPending(pending);
        orderStats.setInProgress(inProgress);
        orderStats.setCompleted(completed);
        orderStats.setCancelled(cancelled);
        
        return orderStats;
    }

    /**
     * 获取今日排班数
     */
    private Integer getTodayScheduleCount(String auntOneId) {
        // TODO: 实现从数据库查询今日排班数
        // 这里需要根据实际的数据库表结构来实现
        return 3; // 临时返回固定值
    }

    /**
     * 获取本月预估收入
     */
    private BigDecimal getMonthlyIncome(String auntOneId) {
        // TODO: 实现从数据库查询本月预估收入
        // 这里需要根据实际的数据库表结构来实现
        return new BigDecimal("8500.00"); // 临时返回固定值
    }

    /**
     * 获取本月服务时长
     */
    private Integer getMonthlyServiceHours(String auntOneId) {
        // TODO: 实现从数据库查询本月服务时长
        // 这里需要根据实际的数据库表结构来实现
        return 88; // 临时返回固定值
    }

    /**
     * 根据状态获取订单数量
     */
    private Integer getOrderCountByStatus(String auntOneId, String status) {
        // TODO: 实现从数据库查询指定状态的订单数量
        // 这里需要根据实际的数据库表结构来实现
        switch (status) {
            case "pending":
                return 1;
            case "in_progress":
                return 2;
            case "completed":
                return 28;
            case "cancelled":
                return 2;
            default:
                return 0;
        }
    }

} 