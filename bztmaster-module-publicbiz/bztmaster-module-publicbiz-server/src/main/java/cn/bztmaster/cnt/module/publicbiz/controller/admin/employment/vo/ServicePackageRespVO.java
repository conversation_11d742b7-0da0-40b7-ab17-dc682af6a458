package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "就业服务-服务套餐 Response VO")
public class ServicePackageRespVO {
    @Schema(description = "套餐ID")
    private Long id;

    @Schema(description = "套餐名称")
    private String name;

    @Schema(description = "服务分类")
    private String category;

    @Schema(description = "套餐主图URL")
    private String thumbnail;

    @Schema(description = "套餐价格")
    private BigDecimal price;

    @Schema(description = "原价")
    private BigDecimal originalPrice;

    @Schema(description = "价格单位")
    private String unit;

    @Schema(description = "服务时长")
    private String serviceDuration;

    @Schema(description = "套餐类型")
    private String packageType;

    @Schema(description = "任务拆分规则")
    private String taskSplitRule;

    @Schema(description = "服务描述")
    private String serviceDescription;

    @Schema(description = "详细服务内容")
    private String serviceDetails;

    @Schema(description = "服务流程")
    private String serviceProcess;

    @Schema(description = "购买须知")
    private String purchaseNotice;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "审核状态")
    private String auditStatus;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Schema(description = "所属机构ID")
    private Long agencyId;

    @Schema(description = "所属机构名称")
    private String agencyName;

    @Schema(description = "预约时间范围")
    private Integer advanceBookingDays;

    @Schema(description = "时间选择模式")
    private String timeSelectionMode;

    @Schema(description = "预约模式")
    private String appointmentMode;

    @Schema(description = "服务开始时间")
    private String serviceStartTime;

    @Schema(description = "地址设置")
    private String addressSetting;

    @Schema(description = "最大预约天数")
    private Integer maxBookingDays;

    @Schema(description = "取消政策")
    private String cancellationPolicy;

    @Schema(description = "轮播图列表")
    private List<ServicePackageCarouselRespVO> carouselList;

    @Schema(description = "特色标签列表")
    private List<ServicePackageFeatureRespVO> featureList;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
} 