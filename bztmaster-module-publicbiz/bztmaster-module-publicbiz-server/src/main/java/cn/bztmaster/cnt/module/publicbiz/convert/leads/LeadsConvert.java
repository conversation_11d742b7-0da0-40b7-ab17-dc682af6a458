package cn.bztmaster.cnt.module.publicbiz.convert.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsAssignReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadsSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.List;

/**
 * 线索 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LeadsConvert {

    LeadsConvert INSTANCE = Mappers.getMapper(LeadsConvert.class);

    /**
     * 将 DO 转换为 VO
     *
     * @param bean DO 对象
     * @return VO 对象
     */
    @Mapping(target = "leadSourceDesc", source = "leadSource", qualifiedByName = "getLeadSourceDesc")
    @Mapping(target = "businessModuleDesc", source = "businessModule", qualifiedByName = "getBusinessModuleDesc")
    @Mapping(target = "leadStatusDesc", source = "leadStatus", qualifiedByName = "getLeadStatusDesc")
    @Mapping(target = "createMethodDesc", source = "createMethod", qualifiedByName = "getCreateMethodDesc")
    @Mapping(target = "createTimeFormatted", source = "createTime", qualifiedByName = "formatCreateTime")
    LeadsRespVO convert(LeadInfoDO bean);

    /**
     * 将 DO 列表转换为 VO 列表
     *
     * @param list DO 列表
     * @return VO 列表
     */
    List<LeadsRespVO> convertList(List<LeadInfoDO> list);

    /**
     * 将分页 DO 转换为分页 VO
     *
     * @param page 分页 DO
     * @return 分页 VO
     */
    PageResult<LeadsRespVO> convertPage(PageResult<LeadInfoDO> page);

    /**
     * 将 VO 转换为 DTO
     *
     * @param bean VO 对象
     * @return DTO 对象
     */
    LeadsSaveReqDTO convert(LeadsSaveReqVO bean);

    /**
     * 将 VO 转换为 DTO
     *
     * @param bean VO 对象
     * @return DTO 对象
     */
    LeadsPageReqDTO convert(LeadsPageReqVO bean);

    /**
     * 将 VO 转换为 DTO
     *
     * @param bean VO 对象
     * @return DTO 对象
     */
    @Mapping(target = "leadId", ignore = true) // leadId需要在Service层设置
    @Mapping(target = "assignRemark", source = "remark")
    LeadsAssignReqDTO convert(LeadAssignReqVO bean);

    /**
     * 将 DO 转换为 DTO
     *
     * @param bean DO 对象
     * @return DTO 对象
     */
    @Mapping(target = "leadSourceDesc", source = "leadSource", qualifiedByName = "getLeadSourceDesc")
    @Mapping(target = "businessModuleDesc", source = "businessModule", qualifiedByName = "getBusinessModuleDesc")
    @Mapping(target = "leadStatusDesc", source = "leadStatus", qualifiedByName = "getLeadStatusDesc")
    @Mapping(target = "createMethodDesc", source = "createMethod", qualifiedByName = "getCreateMethodDesc")
    LeadsRespDTO convertToDTO(LeadInfoDO bean);

    /**
     * 将 DO 列表转换为 DTO 列表
     *
     * @param list DO 列表
     * @return DTO 列表
     */
    List<LeadsRespDTO> convertListToDTO(List<LeadInfoDO> list);

    /**
     * 将分页 DO 转换为分页 DTO
     *
     * @param page 分页 DO
     * @return 分页 DTO
     */
    PageResult<LeadsRespDTO> convertPageToDTO(PageResult<LeadInfoDO> page);

    /**
     * 将 DTO 转换为 DO
     *
     * @param bean DTO 对象
     * @return DO 对象
     */
    LeadInfoDO convertToDO(LeadsSaveReqDTO bean);

    /**
     * 更新 DO 对象
     *
     * @param bean DTO 对象
     * @param target DO 对象
     */
    void updateDO(LeadsSaveReqDTO bean, @MappingTarget LeadInfoDO target);

    /**
     * 获取线索来源描述
     *
     * @param leadSource 线索来源类型
     * @return 线索来源描述
     */
    @Named("getLeadSourceDesc")
    default String getLeadSourceDesc(Integer leadSource) {
        if (leadSource == null) {
            return null;
        }
        LeadSourceEnum leadSourceEnum = LeadSourceEnum.getByType(leadSource);
        return leadSourceEnum != null ? leadSourceEnum.getDesc() : null;
    }

    /**
     * 获取业务模块描述
     *
     * @param businessModule 业务模块类型
     * @return 业务模块描述
     */
    @Named("getBusinessModuleDesc")
    default String getBusinessModuleDesc(Integer businessModule) {
        if (businessModule == null) {
            return null;
        }
        BusinessModuleEnum businessModuleEnum = BusinessModuleEnum.getByType(businessModule);
        return businessModuleEnum != null ? businessModuleEnum.getDesc() : null;
    }

    /**
     * 获取线索状态描述
     *
     * @param leadStatus 线索状态类型
     * @return 线索状态描述
     */
    @Named("getLeadStatusDesc")
    default String getLeadStatusDesc(Integer leadStatus) {
        if (leadStatus == null) {
            return null;
        }
        LeadStatusEnum leadStatusEnum = LeadStatusEnum.getByType(leadStatus);
        return leadStatusEnum != null ? leadStatusEnum.getDesc() : null;
    }

    /**
     * 获取创建方式描述
     *
     * @param createMethod 创建方式类型
     * @return 创建方式描述
     */
    @Named("getCreateMethodDesc")
    default String getCreateMethodDesc(Integer createMethod) {
        if (createMethod == null) {
            return null;
        }
        CreateMethodEnum createMethodEnum = CreateMethodEnum.getByType(createMethod);
        return createMethodEnum != null ? createMethodEnum.getDesc() : null;
    }

    /**
     * 格式化创建时间
     *
     * @param createTime 创建时间
     * @return 格式化后的时间字符串 (yyyy-MM-dd)
     */
    @Named("formatCreateTime")
    default String formatCreateTime(LocalDateTime createTime) {
        if (createTime == null) {
            return null;
        }
        return createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}