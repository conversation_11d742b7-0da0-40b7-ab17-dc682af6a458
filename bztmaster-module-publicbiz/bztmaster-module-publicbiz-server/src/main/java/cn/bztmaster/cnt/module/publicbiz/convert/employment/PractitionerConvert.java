package cn.bztmaster.cnt.module.publicbiz.convert.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface PractitionerConvert {
    PractitionerConvert INSTANCE = Mappers.getMapper(PractitionerConvert.class);

    PractitionerDO convert(PractitionerSaveReqVO bean);

    PractitionerDO convert(PractitionerUpdateReqVO bean);

    PractitionerRespVO convert(PractitionerDO bean);

    List<PractitionerRespVO> convertList(List<PractitionerDO> list);

    PageResult<PractitionerRespVO> convertPage(PageResult<PractitionerDO> page);

    PractitionerExcelVO convertExcel(PractitionerDO bean);

    List<PractitionerExcelVO> convertExcelList(List<PractitionerDO> list);
} 