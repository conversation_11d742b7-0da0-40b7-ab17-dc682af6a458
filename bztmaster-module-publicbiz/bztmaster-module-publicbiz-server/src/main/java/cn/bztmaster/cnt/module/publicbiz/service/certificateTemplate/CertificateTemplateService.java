package cn.bztmaster.cnt.module.publicbiz.service.certificateTemplate;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplatePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.certificateTemplate.vo.CertificateTemplateStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.certificateTemplate.CertificateTemplateDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 证书模板 Service 接口
 *
 * <AUTHOR>
 */
public interface CertificateTemplateService {

    /**
     * 创建证书模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCertificateTemplate(@Valid CertificateTemplateSaveReqVO createReqVO);

    /**
     * 更新证书模板
     *
     * @param updateReqVO 更新信息
     */
    void updateCertificateTemplate(@Valid CertificateTemplateSaveReqVO updateReqVO);

    /**
     * 删除证书模板
     *
     * @param id 编号
     */
    void deleteCertificateTemplate(Long id);

    /**
     * 获得证书模板
     *
     * @param id 编号
     * @return 证书模板
     */
    CertificateTemplateDO getCertificateTemplate(Long id);

    /**
     * 获得证书模板分页
     *
     * @param pageReqVO 分页查询
     * @return 证书模板分页
     */
    PageResult<CertificateTemplateDO> getCertificateTemplatePage(CertificateTemplatePageReqVO pageReqVO);

    /**
     * 获得证书模板列表
     *
     * @param listReqVO 列表查询
     * @return 证书模板列表
     */
    List<CertificateTemplateDO> getCertificateTemplateList(CertificateTemplateListReqVO listReqVO);

    /**
     * 更新证书模板状态
     *
     * @param id     编号
     * @param status 状态
     */
    void updateCertificateTemplateStatus(Long id, String status);

    /**
     * 获得证书模板统计
     *
     * @return 统计信息
     */
    CertificateTemplateStatisticsRespVO getCertificateTemplateStatistics();

    /**
     * 校验证书模板是否存在
     *
     * @param id 编号
     */
    void validateCertificateTemplateExists(Long id);

}