package cn.bztmaster.cnt.module.publicbiz.service.employer;

import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentitySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO;

import javax.validation.Valid;

/**
 * 小程序用户最后登录身份记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MpUserLastLoginIdentityService {

    /**
     * 创建小程序用户最后登录身份记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO createReqVO);

    /**
     * 更新小程序用户最后登录身份记录
     *
     * @param updateReqVO 更新信息
     */
    void updateMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO updateReqVO);

    /**
     * 删除小程序用户最后登录身份记录
     *
     * @param id 编号
     */
    void deleteMpUserLastLoginIdentity(Long id);

    /**
     * 获得小程序用户最后登录身份记录
     *
     * @param id 编号
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentity(Long id);

    /**
     * 根据用户ID和openid获得小程序用户最后登录身份记录
     *
     * @param userId 用户ID
     * @param openid 微信openid
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserIdAndOpenid(Long userId, String openid);

    /**
     * 根据openid获得小程序用户最后登录身份记录
     *
     * @param openid 微信openid
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByOpenid(String openid);

    /**
     * 根据用户ID获得小程序用户最后登录身份记录
     *
     * @param userId 用户ID
     * @return 小程序用户最后登录身份记录
     */
    MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserId(Long userId);

    /**
     * 保存或更新小程序用户最后登录身份记录
     *
     * @param saveReqVO 保存信息
     * @return 编号
     */
    Long saveOrUpdateMpUserLastLoginIdentity(@Valid MpUserLastLoginIdentitySaveReqVO saveReqVO);

}