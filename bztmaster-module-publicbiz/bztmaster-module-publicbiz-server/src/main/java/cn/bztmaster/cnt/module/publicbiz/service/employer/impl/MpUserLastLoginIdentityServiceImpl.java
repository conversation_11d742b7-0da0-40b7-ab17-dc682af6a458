package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentityRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.MpUserLastLoginIdentitySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employer.MpUserLastLoginIdentityConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserLastLoginIdentityDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserLastLoginIdentityMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employer.MpUserLastLoginIdentityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.MP_USER_LAST_LOGIN_IDENTITY_NOT_EXISTS;

/**
 * 小程序用户最后登录身份记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MpUserLastLoginIdentityServiceImpl implements MpUserLastLoginIdentityService {

    @Resource
    private MpUserLastLoginIdentityMapper mpUserLastLoginIdentityMapper;

    @Override
    public Long createMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO createReqVO) {
        // 插入
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = MpUserLastLoginIdentityConvert.INSTANCE
                .convert(createReqVO);
        mpUserLastLoginIdentityMapper.insert(mpUserLastLoginIdentity);
        // 返回
        return mpUserLastLoginIdentity.getId();
    }

    @Override
    public void updateMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO updateReqVO) {
        // 校验存在
        if (updateReqVO.getId() != null) {
            validateMpUserLastLoginIdentityExists(updateReqVO.getId());
        }
        // 更新
        MpUserLastLoginIdentityDO updateObj = MpUserLastLoginIdentityConvert.INSTANCE.convert(updateReqVO);
        mpUserLastLoginIdentityMapper.updateById(updateObj);
    }

    @Override
    public void deleteMpUserLastLoginIdentity(Long id) {
        // 校验存在
        validateMpUserLastLoginIdentityExists(id);
        // 删除
        mpUserLastLoginIdentityMapper.deleteById(id);
    }

    private void validateMpUserLastLoginIdentityExists(Long id) {
        if (mpUserLastLoginIdentityMapper.selectById(id) == null) {
            throw ServiceExceptionUtil.exception(MP_USER_LAST_LOGIN_IDENTITY_NOT_EXISTS);
        }
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentity(Long id) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectById(id);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserIdAndOpenid(Long userId, String openid) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper
                .selectByUserIdAndOpenid(userId, openid);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByOpenid(String openid) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectByOpenid(openid);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public MpUserLastLoginIdentityRespVO getMpUserLastLoginIdentityByUserId(Long userId) {
        MpUserLastLoginIdentityDO mpUserLastLoginIdentity = mpUserLastLoginIdentityMapper.selectByUserId(userId);
        return MpUserLastLoginIdentityConvert.INSTANCE.convert(mpUserLastLoginIdentity);
    }

    @Override
    public Long saveOrUpdateMpUserLastLoginIdentity(MpUserLastLoginIdentitySaveReqVO saveReqVO) {
        // 先查询是否已存在
        MpUserLastLoginIdentityDO existingRecord = mpUserLastLoginIdentityMapper.selectByUserIdAndOpenid(
                saveReqVO.getUserId(), saveReqVO.getOpenid());

        if (existingRecord != null) {
            // 存在则更新
            saveReqVO.setId(existingRecord.getId());
            MpUserLastLoginIdentityDO updateObj = MpUserLastLoginIdentityConvert.INSTANCE.convert(saveReqVO);
            updateObj.setUpdateTime(LocalDateTime.now());
            mpUserLastLoginIdentityMapper.updateById(updateObj);
            return existingRecord.getId();
        } else {
            // 不存在则创建
            return createMpUserLastLoginIdentity(saveReqVO);
        }
    }

}