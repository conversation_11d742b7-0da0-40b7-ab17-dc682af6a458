package cn.bztmaster.cnt.module.publicbiz.service.category;

import cn.bztmaster.cnt.framework.common.enums.CommonStatusEnum;
import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategoryListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.category.vo.ServiceCategorySaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.category.ServiceCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.category.ServiceCategoryMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 服务分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ServiceCategoryServiceImpl implements ServiceCategoryService {

    @Resource
    private ServiceCategoryMapper categoryMapper;

    @Override
    public Long createCategory(ServiceCategorySaveReqVO createReqVO) {
        // 插入
        ServiceCategoryDO category = BeanUtils.toBean(createReqVO, ServiceCategoryDO.class);
        categoryMapper.insert(category);
        // 返回
        return category.getId();
    }

    @Override
    public void updateCategory(ServiceCategorySaveReqVO updateReqVO) {
        // 校验存在
        validateCategory(updateReqVO.getId());
        // 更新
        ServiceCategoryDO updateObj = BeanUtils.toBean(updateReqVO, ServiceCategoryDO.class);
        categoryMapper.updateById(updateObj);
    }

    @Override
    public void deleteCategory(Long id) {
        // 校验存在
        validateCategory(id);
        // 校验是否有子分类
        if (categoryMapper.selectCountByParentId(id) > 0) {
            throw ServiceExceptionUtil.exception(CATEGORY_EXISTS_CHILDREN);
        }
        // 删除
        categoryMapper.deleteById(id);
    }

    @Override
    public ServiceCategoryDO getCategory(Long id) {
        return categoryMapper.selectById(id);
    }

    @Override
    public void validateCategory(Long id) {
        ServiceCategoryDO category = categoryMapper.selectById(id);
        if (category == null) {
            throw ServiceExceptionUtil.exception(CATEGORY_NOT_EXISTS);
        }
    }

    @Override
    public Integer getCategoryLevel(Long id) {
        ServiceCategoryDO category = categoryMapper.selectById(id);
        if (category == null) {
            return 0;
        }
        if (ServiceCategoryDO.PARENT_ID_NULL.equals(category.getParentId())) {
            return 1;
        }
        ServiceCategoryDO parent = categoryMapper.selectById(category.getParentId());
        if (parent == null) {
            return 1;
        }
        if (ServiceCategoryDO.PARENT_ID_NULL.equals(parent.getParentId())) {
            return 2;
        }
        ServiceCategoryDO grandParent = categoryMapper.selectById(parent.getParentId());
        if (grandParent == null) {
            return 2;
        }
        return 3;
    }

    @Override
    public List<ServiceCategoryDO> getCategoryList(ServiceCategoryListReqVO listReqVO) {
        return categoryMapper.selectList(listReqVO);
    }

    @Override
    public PageResult<ServiceCategoryDO> getCategoryPage(ServiceCategoryListReqVO listReqVO) {
        return categoryMapper.selectPage(listReqVO);
    }

    @Override
    public List<ServiceCategoryDO> getEnableCategoryList() {
        return categoryMapper.selectListByStatus(CommonStatusEnum.ENABLE.getStatus());
    }

    @Override
    public List<ServiceCategoryDO> getEnableCategoryList(List<Long> ids) {
        return categoryMapper.selectListByIdAndStatus(ids, CommonStatusEnum.ENABLE.getStatus());
    }

    @Override
    public void validateCategoryList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得服务分类信息
        List<ServiceCategoryDO> categories = categoryMapper.selectBatchIds(ids);
        Map<Long, ServiceCategoryDO> categoryMap = categories.stream()
                .collect(Collectors.toMap(ServiceCategoryDO::getId, category -> category));
        // 校验
        ids.forEach(id -> {
            ServiceCategoryDO category = categoryMap.get(id);
            if (category == null) {
                throw ServiceExceptionUtil.exception(CATEGORY_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(category.getStatus())) {
                throw ServiceExceptionUtil.exception(CATEGORY_NOT_ENABLE);
            }
            // 校验层级
            Integer level = getCategoryLevel(id);
            if (level > ServiceCategoryDO.CATEGORY_LEVEL) {
                throw ServiceExceptionUtil.exception(CATEGORY_LEVEL_TOO_DEEP);
            }
        });
    }

} 