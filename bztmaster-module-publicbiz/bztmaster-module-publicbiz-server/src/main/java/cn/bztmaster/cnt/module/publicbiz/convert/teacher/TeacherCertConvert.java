package cn.bztmaster.cnt.module.publicbiz.convert.teacher;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherCertDO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TeacherCertConvert {
    TeacherCertConvert INSTANCE = Mappers.getMapper(TeacherCertConvert.class);
    TeacherCertDO convert(TeacherCertSaveReqVO bean);
    TeacherCertRespVO convert(TeacherCertDO bean);
    List<TeacherCertRespVO> convertList(List<TeacherCertDO> list);
} 