package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分类层级枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum QuestionCategoryLevelEnum {

    LEVEL_1(1, "一级分类"),
    LEVEL_2(2, "二级分类"),
    LEVEL_3(3, "三级分类");

    /**
     * 分类层级值
     */
    private final Integer value;

    /**
     * 分类层级说明
     */
    private final String description;

    /**
     * 根据值获取枚举
     *
     * @param value 值
     * @return 枚举
     */
    public static QuestionCategoryLevelEnum getByValue(Integer value) {
        for (QuestionCategoryLevelEnum levelEnum : values()) {
            if (levelEnum.getValue().equals(value)) {
                return levelEnum;
            }
        }
        return null;
    }

    /**
     * 校验分类层级是否有效
     *
     * @param value 值
     * @return 是否有效
     */
    public static boolean isValid(Integer value) {
        return getByValue(value) != null;
    }

}
