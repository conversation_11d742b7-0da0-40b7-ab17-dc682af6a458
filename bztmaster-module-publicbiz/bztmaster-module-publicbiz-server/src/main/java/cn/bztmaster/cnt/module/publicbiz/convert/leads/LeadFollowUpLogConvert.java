package cn.bztmaster.cnt.module.publicbiz.convert.leads;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogPageReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.LeadFollowUpLogSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import java.util.List;

/**
 * 线索跟进记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LeadFollowUpLogConvert {

    LeadFollowUpLogConvert INSTANCE = Mappers.getMapper(LeadFollowUpLogConvert.class);

    /**
     * 将 DO 转换为 VO
     *
     * @param bean DO 对象
     * @return VO 对象
     */
    @Mapping(target = "createTimeFormatted", source = "createTime", qualifiedByName = "formatCreateTime")
    LeadFollowUpLogRespVO convert(LeadFollowUpLogDO bean);

    /**
     * 将 DO 列表转换为 VO 列表
     *
     * @param list DO 列表
     * @return VO 列表
     */
    List<LeadFollowUpLogRespVO> convertList(List<LeadFollowUpLogDO> list);

    /**
     * 将分页 DO 转换为分页 VO
     *
     * @param page 分页 DO
     * @return 分页 VO
     */
    PageResult<LeadFollowUpLogRespVO> convertPage(PageResult<LeadFollowUpLogDO> page);

    /**
     * 将 VO 转换为 DTO
     *
     * @param bean VO 对象
     * @return DTO 对象
     */
    LeadFollowUpLogSaveReqDTO convert(LeadFollowUpLogSaveReqVO bean);

    /**
     * 将 VO 转换为 DTO
     *
     * @param bean VO 对象
     * @return DTO 对象
     */
    LeadFollowUpLogPageReqDTO convert(LeadFollowUpLogPageReqVO bean);
    
    /**
     * 将 DO 转换为 DTO
     *
     * @param bean DO 对象
     * @return DTO 对象
     */
    LeadFollowUpLogRespDTO convertToDTO(LeadFollowUpLogDO bean);
    
    /**
     * 将 DO 列表转换为 DTO 列表
     *
     * @param list DO 列表
     * @return DTO 列表
     */
    List<LeadFollowUpLogRespDTO> convertListToDTO(List<LeadFollowUpLogDO> list);
    
    /**
     * 将分页 DO 转换为分页 DTO
     *
     * @param page 分页 DO
     * @return 分页 DTO
     */
    PageResult<LeadFollowUpLogRespDTO> convertPageToDTO(PageResult<LeadFollowUpLogDO> page);
    
    /**
     * 将 DTO 转换为 DO
     *
     * @param bean DTO 对象
     * @return DO 对象
     */
    LeadFollowUpLogDO convertToDO(LeadFollowUpLogSaveReqDTO bean);
    
    /**
     * 更新 DO 对象
     *
     * @param bean DTO 对象
     * @param target DO 对象
     */
    void updateDO(LeadFollowUpLogSaveReqDTO bean, @MappingTarget LeadFollowUpLogDO target);

    /**
     * 格式化创建时间
     *
     * @param createTime 创建时间
     * @return 格式化后的时间字符串 (yyyy-MM-dd HH:mm)
     */
    @Named("formatCreateTime")
    default String formatCreateTime(LocalDateTime createTime) {
        if (createTime == null) {
            return null;
        }
        return createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }
}