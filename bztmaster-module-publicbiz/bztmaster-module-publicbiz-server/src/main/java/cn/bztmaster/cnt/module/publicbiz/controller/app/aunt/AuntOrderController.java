package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntOrderConfirmReqVO;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨订单")
@RestController
@RequestMapping("/publicbiz/aunt/order")
@Validated
public class AuntOrderController {

    @Resource
    private AuntOrderService auntOrderService;

    @GetMapping("/list")
    @Operation(summary = "获得阿姨订单列表")
    @PermitAll
    public CommonResult<PageResult<AuntOrderListRespVO>> getOrderList(@Valid AuntOrderListReqVO reqVO) {
        // 获取当前登录阿姨OneID
        String auntOneId = SecurityFrameworkUtils.getLoginUserId().toString();
        
        // 设置阿姨OneID
        reqVO.setAuntOneId(auntOneId);
        
        // 获取订单列表
        PageResult<AuntOrderListRespVO> result = auntOrderService.getOrderList(reqVO);
        
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得阿姨订单详情")
    @Parameter(name = "orderId", description = "订单ID", required = true)
    @PermitAll
    public CommonResult<AuntOrderDetailRespVO> getOrderDetail(@RequestParam("orderId") Long orderId) {
        // 获取当前登录阿姨OneID
        String auntOneId = SecurityFrameworkUtils.getLoginUserId().toString();
        
        // 获取订单详情
        AuntOrderDetailRespVO orderDetail = auntOrderService.getOrderDetail(orderId, auntOneId);
        
        return success(orderDetail);
    }

    @PostMapping("/confirm")
    @Operation(summary = "确认订单")
    @PermitAll
    public CommonResult<Boolean> confirmOrder(@Valid @RequestBody AuntOrderConfirmReqVO reqVO) {
        // 获取当前登录阿姨OneID
        String auntOneId = SecurityFrameworkUtils.getLoginUserId().toString();
        
        // 确认订单
        auntOrderService.confirmOrder(reqVO.getOrderId(), auntOneId);
        
        return success(Boolean.TRUE);
    }

} 