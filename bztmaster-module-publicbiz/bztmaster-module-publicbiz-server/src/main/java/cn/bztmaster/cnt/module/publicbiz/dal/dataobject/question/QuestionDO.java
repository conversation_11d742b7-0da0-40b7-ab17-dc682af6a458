package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 考题主表 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("publicbiz_question")
@Schema(description = "考题主表 DO")
public class QuestionDO extends BaseDO {

    /**
     * 主键，自增
     */
    @TableId
    @Schema(description = "主键，自增")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 一级分类名称，如：职业技能等级认定
     */
    @Schema(description = "一级分类名称")
    private String level1Name;

    /**
     * 一级分类代码，如：ZY001
     */
    @Schema(description = "一级分类代码")
    private String level1Code;

    /**
     * 二级分类名称，如：家政服务类
     */
    @Schema(description = "二级分类名称")
    private String level2Name;

    /**
     * 二级分类代码，如：JZ001
     */
    @Schema(description = "二级分类代码")
    private String level2Code;

    /**
     * 三级分类名称，如：家政服务员
     */
    @Schema(description = "三级分类名称")
    private String level3Name;

    /**
     * 三级分类代码，如：JZFW001
     */
    @Schema(description = "三级分类代码")
    private String level3Code;

    /**
     * 认定点名称，如：职业道德基础
     */
    @Schema(description = "认定点名称")
    private String certName;

    /**
     * 认定点代码，如：KP001
     */
    @Schema(description = "认定点代码")
    private String certCode;

    /**
     * 题干内容
     */
    @Schema(description = "题干内容")
    private String title;

    /**
     * 题型：单选题、多选题、判断题、简答题、填空题、材料题、排序题、匹配题、文件上传题
     */
    @Schema(description = "题型")
    private String type;

    /**
     * 参考答案
     */
    @Schema(description = "参考答案")
    private String answer;

    /**
     * 业务模块：家政业务、高校业务、培训业务、认证业务
     */
    @Schema(description = "业务模块")
    private String biz;

    /**
     * 业务模块名称
     */
    @Schema(description = "业务模块名称")
    private String bizName;

    /**
     * 难度等级：1-简单，2-中等，3-困难
     */
    @Schema(description = "难度等级")
    private Integer difficulty;

    /**
     * 题目分值
     */
    @Schema(description = "题目分值")
    private BigDecimal score;

    /**
     * 答题时间限制（秒），0表示无限制
     */
    @Schema(description = "答题时间限制（秒）")
    private Integer timeLimit;

    /**
     * 题目解析
     */
    @Schema(description = "题目解析")
    private String explanation;

    /**
     * 关键词，用逗号分隔
     */
    @Schema(description = "关键词")
    private String keywords;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 创建人姓名
     */
    @Schema(description = "创建人姓名")
    private String creatorName;

}
