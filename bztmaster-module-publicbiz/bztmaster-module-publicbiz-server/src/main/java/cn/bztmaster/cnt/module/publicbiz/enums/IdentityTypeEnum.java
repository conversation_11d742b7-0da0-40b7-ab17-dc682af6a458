package cn.bztmaster.cnt.module.publicbiz.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 身份类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IdentityTypeEnum {

    EMPLOYER("EMPLOYER", "雇主"),
    AUNT("AUNT", "阿姨"),
    AGENCY("AGENCY", "机构");

    /**
     * 类型
     */
    private final String type;
    /**
     * 描述
     */
    private final String description;

}