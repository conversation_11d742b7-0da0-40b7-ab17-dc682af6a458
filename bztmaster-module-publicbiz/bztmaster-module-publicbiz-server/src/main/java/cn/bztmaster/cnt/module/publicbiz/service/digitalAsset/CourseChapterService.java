package cn.bztmaster.cnt.module.publicbiz.service.digitalAsset;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.digitalAsset.vo.*;
import java.util.List;

/**
 * 课程章节 Service 接口
 * 
 * <AUTHOR>
 */
public interface CourseChapterService {

    /**
     * 获取课程章节列表（包含课时）
     * 
     * @param courseId 课程ID
     * @return 章节列表
     */
    List<CourseChapterRespVO> listCourseChapter(Long courseId);

    /**
     * 创建章节
     * 
     * @param reqVO 创建信息
     * @return 章节ID
     */
    Long createCourseChapter(CourseChapterSaveReqVO reqVO);

    /**
     * 更新章节
     * 
     * @param reqVO 更新信息
     */
    void updateCourseChapter(CourseChapterSaveReqVO reqVO);

    /**
     * 删除章节
     * 
     * @param id 章节ID
     */
    void deleteCourseChapter(Long id);
}
