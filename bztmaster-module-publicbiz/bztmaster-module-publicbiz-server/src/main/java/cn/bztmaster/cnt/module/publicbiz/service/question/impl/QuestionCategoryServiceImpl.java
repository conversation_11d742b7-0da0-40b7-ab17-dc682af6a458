package cn.bztmaster.cnt.module.publicbiz.service.question.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.question.QuestionCategoryConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionCategoryDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.question.QuestionCategoryMapper;
import cn.bztmaster.cnt.module.publicbiz.service.question.QuestionCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.*;

/**
 * 考题分类服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuestionCategoryServiceImpl implements QuestionCategoryService {

    @Resource
    private QuestionCategoryMapper questionCategoryMapper;

    @Override
    public PageResult<QuestionCategoryRespVO> pageQuestionCategory(QuestionCategoryPageReqVO reqVO) {
        PageResult<QuestionCategoryDO> pageResult = questionCategoryMapper.selectPage(reqVO);
        return QuestionCategoryConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public List<QuestionCategoryRespVO> listQuestionCategory(String biz, List<Integer> levels, Long parentId) {
        // 校验层级参数
        if (levels != null && !levels.isEmpty()) {
            for (Integer level : levels) {
                if (level == null || level < 1 || level > 3) {
                    throw exception(QUESTION_CATEGORY_LEVEL_INVALID);
                }
            }
        }

        List<QuestionCategoryDO> list = questionCategoryMapper.selectList(biz, levels, parentId);
        return QuestionCategoryConvert.INSTANCE.convertList(list);
    }

    @Override
    public QuestionCategoryRespVO getQuestionCategory(Long id) {
        QuestionCategoryDO category = questionCategoryMapper.selectById(id);
        if (category == null) {
            throw exception(QUESTION_CATEGORY_NOT_EXISTS);
        }
        return QuestionCategoryConvert.INSTANCE.convert(category);
    }

    @Override
    public Long createQuestionCategory(QuestionCategorySaveReqVO reqVO) {
        // 校验分类代码是否重复
        validateCategoryCodeUnique(reqVO.getLevel1Code(), reqVO.getLevel2Code(), reqVO.getLevel3Code(), null);

        // 转换并插入分类
        QuestionCategoryDO category = QuestionCategoryConvert.INSTANCE.convert(reqVO);
        category.setStatus(1); // 默认启用
        category.setCreatorName(SecurityFrameworkUtils.getLoginUserNickname()); // 设置创建人姓名
        questionCategoryMapper.insert(category);

        return category.getId();
    }

    @Override
    public void updateQuestionCategory(QuestionCategorySaveReqVO reqVO) {
        // 校验分类是否存在
        validateQuestionCategoryExists(reqVO.getId());

        // 校验分类代码是否重复
        validateCategoryCodeUnique(reqVO.getLevel1Code(), reqVO.getLevel2Code(), reqVO.getLevel3Code(), reqVO.getId());

        // 更新分类
        QuestionCategoryDO category = QuestionCategoryConvert.INSTANCE.convert(reqVO);
        questionCategoryMapper.updateById(category);
    }

    @Override
    public void deleteQuestionCategory(Long id) {
        // 校验分类是否存在
        validateQuestionCategoryExists(id);

        // 校验分类下是否存在考题，如果存在则不允许删除
        // TODO: 实现校验分类下是否存在考题的逻辑

        // 校验分类下是否存在子分类，如果存在则不允许删除
        validateCategoryHasNoChildren(id);

        // 软删除分类
        questionCategoryMapper.deleteById(id);
    }

    @Override
    public QuestionCategoryDO getQuestionCategoryDO(Long id) {
        return questionCategoryMapper.selectById(id);
    }

    @Override
    public List<QuestionCategoryDO> getQuestionCategoryDOList(Collection<Long> ids) {
        return questionCategoryMapper.selectBatchIds(ids);
    }

    @Override
    public void validateQuestionCategoryExists(Long id) {
        if (questionCategoryMapper.selectById(id) == null) {
            throw exception(QUESTION_CATEGORY_NOT_EXISTS);
        }
    }

    /**
     * 校验分类代码是否唯一
     *
     * @param level1Code 一级分类代码
     * @param level2Code 二级分类代码
     * @param level3Code 三级分类代码
     * @param excludeId 排除的ID
     */
    private void validateCategoryCodeUnique(String level1Code, String level2Code, String level3Code, Long excludeId) {
        QuestionCategoryDO category = questionCategoryMapper.selectByCode(level1Code, level2Code, level3Code, excludeId);
        if (category != null) {
            throw exception(QUESTION_CATEGORY_CODE_DUPLICATE);
        }
    }

    /**
     * 校验分类下是否存在子分类
     *
     * @param parentId 父级分类ID
     */
    private void validateCategoryHasNoChildren(Long parentId) {
        List<QuestionCategoryDO> children = questionCategoryMapper.selectByParentId(parentId);
        if (!children.isEmpty()) {
            throw exception(QUESTION_CATEGORY_HAS_CHILDREN);
        }
    }

}
