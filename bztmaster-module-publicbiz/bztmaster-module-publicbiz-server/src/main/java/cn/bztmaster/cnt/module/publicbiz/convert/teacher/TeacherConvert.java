package cn.bztmaster.cnt.module.publicbiz.convert.teacher;

import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.teacher.TeacherDO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo.*;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper(componentModel = "spring")
public interface TeacherConvert {
    TeacherConvert INSTANCE = Mappers.getMapper(TeacherConvert.class);
    TeacherDO convert(TeacherSaveReqVO bean);
    TeacherDO convert(TeacherUpdateReqVO bean);
    TeacherRespVO convert(TeacherDO bean);
    List<TeacherRespVO> convertList(List<TeacherDO> list);
    PageResult<TeacherRespVO> convertPage(PageResult<TeacherDO> page);

    TeacherListRespVO convertToListVO(TeacherDO bean);
    List<TeacherListRespVO> convertToListVOList(List<TeacherDO> list);
}