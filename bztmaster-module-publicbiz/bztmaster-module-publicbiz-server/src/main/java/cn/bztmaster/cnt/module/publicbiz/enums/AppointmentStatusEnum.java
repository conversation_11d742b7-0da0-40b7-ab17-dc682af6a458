package cn.bztmaster.cnt.module.publicbiz.enums;

import cn.bztmaster.cnt.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 预约状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum AppointmentStatusEnum implements ArrayValuable<String> {

    CONFIRMED("已确认", "预约已确认，可正常使用"),
    PENDING("待确认", "预约待管理员确认"),
    CANCELLED("已取消", "预约已取消");

    public static final String[] ARRAYS = Arrays.stream(values()).map(AppointmentStatusEnum::getStatus).toArray(String[]::new);

    /**
     * 状态值
     */
    private final String status;
    /**
     * 状态描述
     */
    private final String description;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 根据状态值获取枚举
     *
     * @param status 状态值
     * @return 枚举
     */
    public static AppointmentStatusEnum getByStatus(String status) {
        return Arrays.stream(values())
                .filter(item -> item.getStatus().equals(status))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否为有效的预约状态
     *
     * @param status 状态值
     * @return 是否有效
     */
    public static boolean isValid(String status) {
        return getByStatus(status) != null;
    }

    /**
     * 判断是否可取消
     *
     * @param status 状态值
     * @return 是否可取消
     */
    public static boolean isCancellable(String status) {
        return CONFIRMED.getStatus().equals(status) || PENDING.getStatus().equals(status);
    }

    /**
     * 判断是否已确认
     *
     * @param status 状态值
     * @return 是否已确认
     */
    public static boolean isConfirmed(String status) {
        return CONFIRMED.getStatus().equals(status);
    }
}
