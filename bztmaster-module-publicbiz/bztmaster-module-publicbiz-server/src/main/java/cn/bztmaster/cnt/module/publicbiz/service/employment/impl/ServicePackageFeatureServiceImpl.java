package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageFeatureSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.ServicePackageFeatureConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageFeatureDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageFeatureMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.ServicePackageFeatureService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;

import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

@Service
@Validated
@Slf4j
public class ServicePackageFeatureServiceImpl implements ServicePackageFeatureService {

    @Resource
    private ServicePackageFeatureMapper servicePackageFeatureMapper;

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_FEATURE_TYPE, subType = SERVICE_PACKAGE_FEATURE_CREATE_SUB_TYPE, bizNo = "{{#result}}", success = SERVICE_PACKAGE_FEATURE_CREATE_SUCCESS)
    public Long createServicePackageFeature(ServicePackageFeatureSaveReqVO createReqVO) {
        ServicePackageFeatureDO feature = ServicePackageFeatureConvert.INSTANCE.convert(createReqVO);
        feature.setCreateTime(new Date());
        feature.setDeleted(false);
        feature.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        feature.setTenantId(SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId() : 1L);
        servicePackageFeatureMapper.insert(feature);

        // 记录操作日志上下文
        LogRecordContext.putVariable("feature", feature);
        return feature.getId();
    }

    @Override
    @Transactional
    @LogRecord(type = SERVICE_PACKAGE_FEATURE_TYPE, subType = SERVICE_PACKAGE_FEATURE_DELETE_SUB_TYPE, bizNo = "{{#id}}", success = SERVICE_PACKAGE_FEATURE_DELETE_SUCCESS)
    public void deleteServicePackageFeature(Long id) {
        ServicePackageFeatureDO feature = servicePackageFeatureMapper.selectById(id);
        if (feature != null) {
            servicePackageFeatureMapper.deleteById(id);
            // 记录操作日志上下文
            LogRecordContext.putVariable("feature", feature);
        }
    }
} 