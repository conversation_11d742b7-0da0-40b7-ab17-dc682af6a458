<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper">

    <sql id="selectFields">
        id, name, category, thumbnail, price, original_price, unit, service_duration, package_type, task_split_rule,
        service_description, service_details, service_process, purchase_notice, status, advance_booking_days,
        time_selection_mode, appointment_mode, service_start_time, address_setting, max_booking_days, cancellation_policy,
        create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <resultMap id="BaseResultMap" type="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO">
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="category" column="category" />
        <result property="thumbnail" column="thumbnail" />
        <result property="price" column="price" />
        <result property="originalPrice" column="original_price" />
        <result property="unit" column="unit" />
        <result property="serviceDuration" column="service_duration" />
        <result property="packageType" column="package_type" />
        <result property="taskSplitRule" column="task_split_rule" />
        <result property="serviceDescription" column="service_description" />
        <result property="serviceDetails" column="service_details" />
        <result property="serviceProcess" column="service_process" />
        <result property="purchaseNotice" column="purchase_notice" />
        <result property="status" column="status" />
        <result property="advanceBookingDays" column="advance_booking_days" />
        <result property="timeSelectionMode" column="time_selection_mode" />
        <result property="appointmentMode" column="appointment_mode" />
        <result property="serviceStartTime" column="service_start_time" />
        <result property="addressSetting" column="address_setting" />
        <result property="maxBookingDays" column="max_booking_days" />
        <result property="cancellationPolicy" column="cancellation_policy" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="creator" column="creator" />
        <result property="updater" column="updater" />
        <result property="deleted" column="deleted" />
        <result property="tenantId" column="tenant_id" />
    </resultMap>

</mapper> 