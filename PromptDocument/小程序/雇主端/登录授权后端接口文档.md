# 登录授权后端接口文档

## 接口概述

本文档描述了小程序启动时获取用户角色的接口规范，用于根据用户身份动态跳转到不同的首页界面。

## 1. 获取用户角色接口

### 接口信息
- **接口名称**: 获取用户角色
- **接口路径**: `/publicbiz/employer/getUserRole`
- **请求方法**: `POST`
- **接口描述**: 根据微信登录code获取用户角色信息，用于小程序启动时的身份判断

### 请求参数

#### 请求头 (Headers)
```
Content-Type: application/json
```

#### 请求体 (Body)
```json
{
  "code": "微信登录临时code"
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信登录临时code，通过uni.login()获取 |

### 响应格式

#### 成功响应
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "role": "employer",
    "userInfo": {
      "id": "user_123456",
      "openid": "wx_openid_123456",
      "nickname": "用户昵称",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "role": "employer",
      "status": "active",
      "createTime": "2024-01-01 12:00:00",
      "updateTime": "2024-01-01 12:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456"
  }
}
```

#### 失败响应
```json
{
  "code": 1001,
  "msg": "参数错误",
  "data": null
}
```

### 响应参数说明

#### 顶层参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码，0表示成功，非0表示失败 |
| msg | string | 响应消息 |
| data | object | 响应数据，失败时为null |

#### data参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| role | string | 用户角色，枚举值：employer(雇主)、aunt(阿姨)、agency(机构) |
| userInfo | object | 用户基本信息 |
| token | string | 访问令牌，用于后续接口认证 |
| refreshToken | string | 刷新令牌，用于刷新访问令牌 |

#### userInfo参数
| 参数名 | 类型 | 说明 |
|--------|------|------|
| id | string | 用户ID |
| openid | string | 微信openid |
| nickname | string | 用户昵称 |
| avatar | string | 用户头像URL |
| mobile | string | 手机号码 |
| role | string | 用户角色 |
| status | string | 用户状态：active(正常)、inactive(禁用) |
| createTime | string | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| updateTime | string | 更新时间，格式：yyyy-MM-dd HH:mm:ss |

### 角色枚举说明

| 角色值 | 角色名称 | 说明 | 跳转页面 |
|--------|----------|------|----------|
| employer | 雇主 | 普通用户，可以发布服务需求 | /pages/index/index |
| aunt | 阿姨 | 服务提供者，提供家政服务 | /pages/aunt-workbench/index |
| agency | 机构 | 服务机构，管理阿姨和订单 | /pages/agency-workbench/index |

### 响应码说明

| 响应码 | 说明 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 正常处理 |
| 1001 | 参数错误 | 检查code参数是否正确 |
| 1002 | 微信登录失败 | 重新获取code |
| 1003 | 用户不存在 | 引导用户注册 |
| 1004 | 用户被禁用 | 提示用户联系客服 |
| 1005 | 角色权限不足 | 提示用户升级权限 |
| 5000 | 服务器内部错误 | 稍后重试 |

## 2. 接口实现要点

### 2.1 微信登录验证
```java
// 伪代码示例
public class UserRoleController {
    
    @PostMapping("/publicbiz/employer/getUserRole")
    public ApiResponse getUserRole(@RequestBody GetUserRoleRequest request) {
        try {
            // 1. 验证code参数
            if (StringUtils.isEmpty(request.getCode())) {
                return ApiResponse.error(1001, "code参数不能为空");
            }
            
            // 2. 调用微信接口获取openid
            WxLoginResult wxResult = wxService.code2Session(request.getCode());
            if (!wxResult.isSuccess()) {
                return ApiResponse.error(1002, "微信登录失败");
            }
            
            // 3. 根据openid查询用户信息
            User user = userService.getByOpenid(wxResult.getOpenid());
            if (user == null) {
                return ApiResponse.error(1003, "用户不存在");
            }
            
            // 4. 检查用户状态
            if (!"active".equals(user.getStatus())) {
                return ApiResponse.error(1004, "用户被禁用");
            }
            
            // 5. 生成访问令牌
            String token = jwtService.generateToken(user.getId());
            String refreshToken = jwtService.generateRefreshToken(user.getId());
            
            // 6. 构建响应数据
            GetUserRoleResponse response = new GetUserRoleResponse();
            response.setRole(user.getRole());
            response.setUserInfo(convertToUserInfo(user));
            response.setToken(token);
            response.setRefreshToken(refreshToken);
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("获取用户角色失败", e);
            return ApiResponse.error(5000, "服务器内部错误");
        }
    }
}
```

### 2.2 数据库设计建议

#### 小程序用户最后登录身份记录表 (mp_user_last_login_identity)
```sql
CREATE TABLE `mp_user_last_login_identity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `identity_type` varchar(32) NOT NULL COMMENT '身份类型：EMPLOYER-雇主，AUNT-阿姨，AGENCY-机构',
  `identity_id` bigint(20) DEFAULT NULL COMMENT '身份关联ID（如雇主ID、阿姨ID等）',
  `identity_name` varchar(100) DEFAULT NULL COMMENT '身份名称（如雇主姓名、阿姨姓名等）',
  `last_login_time` datetime NOT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `login_status` tinyint(1) DEFAULT 1 COMMENT '登录状态：1-正常，0-异常',
  `session_key` varchar(64) DEFAULT NULL COMMENT '微信session_key',
  `access_token` varchar(255) DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 1 COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序用户最后登录身份记录表';
```

### 2.3 安全考虑

1. **接口安全**
   - 使用HTTPS协议
   - 验证微信登录code的有效性
   - 防止重复请求和恶意调用

2. **数据安全**
   - 敏感信息加密存储
   - 用户状态验证
   - 角色权限校验

3. **令牌管理**
   - 使用JWT生成访问令牌
   - 设置合理的过期时间
   - 支持令牌刷新机制

## 3. 测试用例

### 3.1 正常流程测试
```bash
# 请求示例
curl -X POST https://api.example.com/publicbiz/employer/getUserRole \
  -H "Content-Type: application/json" \
  -d '{"code": "wx_login_code_123456"}'

# 预期响应
{
  "code": 0,
  "msg": "success",
  "data": {
    "role": "employer",
    "userInfo": {
      "id": "user_123456",
      "openid": "wx_openid_123456",
      "nickname": "测试用户",
      "avatar": "https://example.com/avatar.jpg",
      "mobile": "13800138000",
      "role": "employer",
      "status": "active",
      "createTime": "2024-01-01 12:00:00",
      "updateTime": "2024-01-01 12:00:00"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_123456"
  }
}
```

### 3.2 异常情况测试

#### 参数错误
```bash
# 请求：缺少code参数
curl -X POST https://api.example.com/publicbiz/employer/getUserRole \
  -H "Content-Type: application/json" \
  -d '{}'

# 预期响应
{
  "code": 1001,
  "msg": "code参数不能为空",
  "data": null
}
```

#### 微信登录失败
```bash
# 请求：无效的code
curl -X POST https://api.example.com/publicbiz/employer/getUserRole \
  -H "Content-Type: application/json" \
  -d '{"code": "invalid_code"}'

# 预期响应
{
  "code": 1002,
  "msg": "微信登录失败",
  "data": null
}
```

## 4. 部署说明

### 4.1 环境配置
- 确保微信小程序AppID和AppSecret配置正确
- 配置数据库连接信息
- 设置JWT密钥和过期时间

### 4.2 监控告警
- 监控接口调用频率和响应时间
- 设置异常响应码告警
- 记录详细的错误日志

### 4.3 性能优化
- 使用Redis缓存用户信息
- 数据库查询优化
- 接口响应时间控制在200ms以内

## 5. 版本历史

| 版本 | 日期 | 修改内容 |
|------|------|----------|
| v1.0 | 2024-01-01 | 初始版本，支持基本的用户角色获取功能 |

## 6. 联系方式

如有问题，请联系开发团队或查看相关技术文档。
