CREATE TABLE IF NOT EXISTS `pay_app` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `app_key`              varchar(64)   NOT NULL COMMENT '应用标识',
    `name`              varchar(64)   NOT NULL COMMENT '应用名称',
    `status`            tinyint       NOT NULL COMMENT '状态',
    `remark`            varchar(255)           DEFAULT NULL COMMENT '备注',
    `order_notify_url`    varchar(1024) NOT NULL COMMENT '支付结果的回调地址',
    `refund_notify_url` varchar(1024) NOT NULL COMMENT '退款结果的回调地址',
    `transfer_notify_url` varchar(1024) COMMENT '转账结果的回调地址',
    `creator`           varchar(64)            DEFAULT '' COMMENT '创建者',
    `create_time`       datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`           varchar(64)            DEFAULT '' COMMENT '更新者',
    `update_time`       datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`           bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付应用';

CREATE TABLE IF NOT EXISTS `pay_channel` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `code`        varchar(32)    NOT NULL COMMENT '渠道编码',
    `status`      tinyint(4)     NOT NULL COMMENT '状态',
    `remark`      varchar(255)            DEFAULT NULL COMMENT '备注',
    `fee_rate`    double         NOT NULL DEFAULT 0 COMMENT '渠道费率，单位：百分比',
    `app_id`      bigint(20)     NOT NULL COMMENT '应用编号',
    `config`      varchar(10240) NOT NULL COMMENT '支付渠道配置',
    `creator`     varchar(64)    NULL     DEFAULT '' COMMENT '创建者',
    `create_time` datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     varchar(64)    NULL     DEFAULT '' COMMENT '更新者',
    `update_time` datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`     bit(1)         NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付渠道';

CREATE TABLE IF NOT EXISTS `pay_order` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `app_id`               bigint(20)    NOT NULL COMMENT '应用编号',
    `channel_id`           bigint(20)             DEFAULT NULL COMMENT '渠道编号',
    `channel_code`         varchar(32)            DEFAULT NULL COMMENT '渠道编码',
    `merchant_order_id`    varchar(64)   NOT NULL COMMENT '商户订单编号',
    `subject`              varchar(32)   NOT NULL COMMENT '支付标题',
    `body`                 varchar(128)  NOT NULL COMMENT '支付内容',
    `notify_url`           varchar(1024) NOT NULL COMMENT '支付结果的回调地址',
    `price`                bigint(20)    NOT NULL COMMENT '支付金额，单位：分',
    `channel_fee_rate`     double                 DEFAULT 0 COMMENT '渠道手续费，单位：百分比',
    `channel_fee_price`    bigint(20)             DEFAULT 0 COMMENT '渠道手续金额，单位：分',
    `status`               tinyint(4)    NOT NULL COMMENT '支付状态',
    `user_ip`              varchar(50)   NOT NULL COMMENT '用户IP',
    `expire_time`          timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单失效时间',
    `success_time`         datetime(0)            DEFAULT CURRENT_TIMESTAMP COMMENT '订单支付成功时间',
    `notify_time`          datetime(0)            DEFAULT CURRENT_TIMESTAMP COMMENT '订单支付通知时间',
    `extension_id` bigint(20)             DEFAULT NULL COMMENT '支付订单拓展单编号',
    `no`                   varchar(64)   NULL COMMENT '支付订单号',
    `refund_price`         bigint(20)    NOT NULL COMMENT '退款总金额，单位：分',
    `channel_user_id`      varchar(255)           DEFAULT NULL COMMENT '渠道用户编号',
    `channel_order_no`     varchar(64)            DEFAULT NULL COMMENT '渠道订单号',
    `creator`              varchar(64)            DEFAULT '' COMMENT '创建者',
    `create_time`          datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`              varchar(64)            DEFAULT '' COMMENT '更新者',
    `update_time`          datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`              bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付订单';

CREATE TABLE IF NOT EXISTS `pay_order_extension` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `no`           varchar(64)         NOT NULL COMMENT '支付订单号',
    `order_id`           bigint(20)    NOT NULL COMMENT '支付订单编号',
    `channel_id`         bigint(20)    NOT NULL COMMENT '渠道编号',
    `channel_code`       varchar(32)   NOT NULL COMMENT '渠道编码',
    `user_ip`            varchar(50)   NULL     DEFAULT NULL COMMENT '用户IP',
    `status`             tinyint(4)    NOT NULL COMMENT '支付状态',
    `channel_extras`     varchar(1024) NULL     DEFAULT NULL COMMENT '支付渠道的额外参数',
    `channel_error_code`  varchar(64)  NULL COMMENT '调用渠道的错误码',
    `channel_error_msg` varchar(64)    NULL COMMENT '调用渠道的错误提示',
    `channel_notify_data` varchar(1024)  NULL COMMENT '支付渠道异步通知的内容',
    `creator`            varchar(64)   NULL     DEFAULT '' COMMENT '创建者',
    `create_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            varchar(64)   NULL     DEFAULT '' COMMENT '更新者',
    `update_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付订单拓展';

CREATE TABLE IF NOT EXISTS `pay_refund` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `no`           varchar(64)         NOT NULL COMMENT '退款单号',
    `app_id`             bigint(20)    NOT NULL COMMENT '应用编号',
    `channel_id`         bigint(20)    NOT NULL COMMENT '渠道编号',
    `channel_code`       varchar(32)   NOT NULL COMMENT '渠道编码',
    `order_id`           bigint(20)    NOT NULL COMMENT '支付订单编号',
    `order_no`           varchar(64)    NOT NULL COMMENT '支付订单号',
    `merchant_order_id`  varchar(64)   NOT NULL COMMENT '商户订单编号',
    `merchant_refund_id` varchar(64)   NOT NULL COMMENT '商户退款订单号',
    `notify_url`         varchar(1024) NOT NULL COMMENT '异步通知地址',
    `status`             tinyint(4)    NOT NULL COMMENT '退款状态',
    `pay_price`         bigint(20)    NOT NULL COMMENT '支付金额，单位：分',
    `refund_price`      bigint(20)    NOT NULL COMMENT '退款金额，单位：分',
    `reason`             varchar(256)  NOT NULL COMMENT '退款原因',
    `user_ip`            varchar(50)   NULL     DEFAULT NULL COMMENT '用户IP',
    `channel_order_no`   varchar(64)   NOT NULL COMMENT '渠道订单号',
    `channel_refund_no`  varchar(64)   NULL     DEFAULT NULL COMMENT '渠道退款单号',
    `success_time`       datetime(0)   NULL     DEFAULT NULL COMMENT '退款成功时间',
    `channel_error_code` varchar(128)  NULL     DEFAULT NULL COMMENT '渠道调用报错时，错误码',
    `channel_error_msg`  varchar(256)  NULL     DEFAULT NULL COMMENT '渠道调用报错时，错误信息',
    `channel_notify_data` varchar(1024)  NULL COMMENT '支付渠道异步通知的内容',
    `creator`            varchar(64)   NULL     DEFAULT '' COMMENT '创建者',
    `create_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            varchar(64)   NULL     DEFAULT '' COMMENT '更新者',
    `update_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '退款订单';

CREATE TABLE IF NOT EXISTS `pay_notify_task` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `app_id`             bigint(20)    NOT NULL COMMENT '应用编号',
    `type`               tinyint(4)    NOT NULL COMMENT '通知类型',
    `data_id`           bigint(20)    NOT NULL COMMENT '数据编号',
    `merchant_order_id`           varchar(64)    NOT NULL COMMENT '商户订单编号',
    `status`             tinyint(4)    NOT NULL COMMENT '通知状态',
    `next_notify_time`       datetime(0)   NULL     DEFAULT NULL COMMENT '下一次通知时间',
    `last_execute_time`       datetime(0)   NULL     DEFAULT NULL COMMENT '最后一次执行时间',
    `notify_times`         int    NOT NULL COMMENT '当前通知次数',
    `max_notify_times`         int    NOT NULL COMMENT '最大可通知次数',
    `notify_url`         varchar(1024) NOT NULL COMMENT '通知地址',
    `creator`            varchar(64)   NULL     DEFAULT '' COMMENT '创建者',
    `create_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            varchar(64)   NULL     DEFAULT '' COMMENT '更新者',
    `update_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id`           bigint(20)    NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付通知任务';

CREATE TABLE IF NOT EXISTS `pay_notify_log` (
    `id` BIGINT AUTO_INCREMENT COMMENT '主键ID',
    `task_id`             bigint(20)    NOT NULL COMMENT '通知任务编号',
    `notify_times`         int    NOT NULL COMMENT '第几次被通知',
    `response`         varchar(1024) NOT NULL COMMENT '请求地址的响应结果',
    `status`             tinyint(4)    NOT NULL COMMENT '通知状态',
    `creator`            varchar(64)   NULL     DEFAULT '' COMMENT '创建者',
    `create_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`            varchar(64)   NULL     DEFAULT '' COMMENT '更新者',
    `update_time`        datetime(0)   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`            bit(1)        NOT NULL DEFAULT FALSE COMMENT '是否删除',
    `tenant_id` bigint not null default  '0' COMMENT '租户编号',
    PRIMARY KEY (`id`)
) COMMENT = '支付通知日志';
